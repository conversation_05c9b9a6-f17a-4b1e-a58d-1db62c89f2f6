import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Plus } from "lucide-react";
import { QuickBookingData } from "@/types/booking";
import DatabaseManager from "@/lib/database";

interface QuickBookingProps {
  onBookingCreated?: () => void;
}

const QuickBooking: React.FC<QuickBookingProps> = ({ onBookingCreated }) => {
  const [showQuickBookingDialog, setShowQuickBookingDialog] = useState(false);
  const [quickBooking, setQuickBooking] = useState<QuickBookingData>({
    customer_name: "",
    service_type: "service",
    service_id: "",
    date: new Date().toISOString().split("T")[0],
    start_time: "",
    notes: "",
    dress_rental_start_date: "",
    dress_rental_end_date: "",
  });

  const dataManager = DatabaseManager.getInstance();

  const handleQuickBooking = () => {
    if (
      !quickBooking.customer_name ||
      !quickBooking.service_id ||
      !quickBooking.date
    ) {
      alert("Please fill in all required fields.");
      return;
    }

    // Validate dress rental period if dress is selected
    if (quickBooking.service_type === "dress") {
      if (
        !quickBooking.dress_rental_start_date ||
        !quickBooking.dress_rental_end_date
      ) {
        alert("Please specify the rental period for the dress.");
        return;
      }

      if (
        new Date(quickBooking.dress_rental_start_date) >
        new Date(quickBooking.dress_rental_end_date)
      ) {
        alert("Rental start date must be before end date.");
        return;
      }
    }

    // Get services, packages, and dresses for pricing
    const services = dataManager.getServices();
    const packages = dataManager.getPackages();
    const dresses = dataManager.getDresses();

    let totalAmount = 0;
    let serviceIds: number[] = [];
    let packageIds: number[] = [];
    let dressIds: number[] = [];
    let title = "Quick Booking";

    if (quickBooking.service_type === "service") {
      const service = services.find(
        (s) => s.id.toString() === quickBooking.service_id,
      );
      if (service) {
        serviceIds = [service.id];
        totalAmount = service.price;
        title = service.name;
      }
    } else if (quickBooking.service_type === "package") {
      const pkg = packages.find(
        (p) => p.id.toString() === quickBooking.service_id,
      );
      if (pkg) {
        packageIds = [pkg.id];
        totalAmount = pkg.price;
        title = pkg.name;
      }
    } else if (quickBooking.service_type === "dress") {
      const dress = dresses.find(
        (d) => d.id.toString() === quickBooking.service_id,
      );
      if (dress) {
        dressIds = [dress.id];
        totalAmount = dress.rental_price;
        title = dress.name;
      }
    }

    // Create booking object
    const booking = {
      id: Date.now(),
      title,
      notes: quickBooking.notes,
      date: quickBooking.date,
      customer_id: null,
      customer_name: quickBooking.customer_name,
      employee_ids: [],
      start_time: quickBooking.start_time,
      end_time: "",
      service_ids: serviceIds,
      package_ids: packageIds,
      dress_ids: dressIds,
      total_amount: totalAmount,
      deposit: 0,
      discount_percentage: 0,
      status: "Confirmed" as const,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Save to database
    const bookingData = {
      ...booking,
      employee_ids: JSON.stringify(booking.employee_ids),
      service_ids: JSON.stringify(booking.service_ids),
      package_ids: JSON.stringify(booking.package_ids),
      dress_ids: JSON.stringify(booking.dress_ids),
      dress_rental_start_date:
        booking.dress_ids.length > 0
          ? quickBooking.dress_rental_start_date
          : null,
      dress_rental_end_date:
        booking.dress_ids.length > 0
          ? quickBooking.dress_rental_end_date
          : null,
    };
    dataManager.addBooking(bookingData);

    alert("Quick booking created successfully!");
    setShowQuickBookingDialog(false);
    setQuickBooking({
      customer_name: "",
      service_type: "service",
      service_id: "",
      date: new Date().toISOString().split("T")[0],
      start_time: "",
      notes: "",
      dress_rental_start_date: "",
      dress_rental_end_date: "",
    });
    
    // Notify parent component that booking was created
    if (onBookingCreated) {
      onBookingCreated();
    }
  };

  return (
    <>
      <Button
        onClick={() => setShowQuickBookingDialog(true)}
        className="flex items-center space-x-2 h-12 px-6"
      >
        <Plus className="h-5 w-5" />
        <span>Quick New Booking</span>
      </Button>

      {/* Quick Booking Dialog */}
      <Dialog
        open={showQuickBookingDialog}
        onOpenChange={setShowQuickBookingDialog}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Quick New Booking</DialogTitle>
            <DialogDescription>
              Create a simple booking quickly
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="quick-customer">Customer Name *</Label>
              <Input
                id="quick-customer"
                value={quickBooking.customer_name}
                onChange={(e) =>
                  setQuickBooking({
                    ...quickBooking,
                    customer_name: e.target.value,
                  })
                }
                placeholder="Enter customer name"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Service Type *</Label>
                <Select
                  value={quickBooking.service_type}
                  onValueChange={(value) =>
                    setQuickBooking({
                      ...quickBooking,
                      service_type: value as "service" | "package" | "dress",
                      service_id: "",
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="service">Individual Service</SelectItem>
                    <SelectItem value="package">Service Package</SelectItem>
                    <SelectItem value="dress">Dress Rental</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>
                  Select{" "}
                  {quickBooking.service_type === "service"
                    ? "Service"
                    : quickBooking.service_type === "package"
                      ? "Package"
                      : "Dress"}{" "}
                  *
                </Label>
                <Select
                  value={quickBooking.service_id}
                  onValueChange={(value) =>
                    setQuickBooking({ ...quickBooking, service_id: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={`Choose ${quickBooking.service_type}`}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {quickBooking.service_type === "service" &&
                      dataManager.getServices().map((service) => (
                        <SelectItem
                          key={service.id}
                          value={service.id.toString()}
                        >
                          {service.name} - ${service.price}
                        </SelectItem>
                      ))}
                    {quickBooking.service_type === "package" &&
                      dataManager.getPackages().map((pkg) => (
                        <SelectItem key={pkg.id} value={pkg.id.toString()}>
                          {pkg.name} - ${pkg.price}
                        </SelectItem>
                      ))}
                    {quickBooking.service_type === "dress" &&
                      dataManager
                        .getDresses()
                        .filter((d) => d.status === "Available")
                        .map((dress) => (
                          <SelectItem
                            key={dress.id}
                            value={dress.id.toString()}
                          >
                            {dress.name} - ${dress.rental_price}
                          </SelectItem>
                        ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="quick-date">Date *</Label>
                <Input
                  id="quick-date"
                  type="date"
                  value={quickBooking.date}
                  onChange={(e) =>
                    setQuickBooking({ ...quickBooking, date: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="quick-time">Start Time</Label>
                <Input
                  id="quick-time"
                  type="time"
                  value={quickBooking.start_time}
                  onChange={(e) =>
                    setQuickBooking({
                      ...quickBooking,
                      start_time: e.target.value,
                    })
                  }
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="quick-notes">Notes</Label>
              <Textarea
                id="quick-notes"
                value={quickBooking.notes}
                onChange={(e) =>
                  setQuickBooking({ ...quickBooking, notes: e.target.value })
                }
                placeholder="Any additional notes..."
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowQuickBookingDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleQuickBooking}>Create Booking</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default QuickBooking;
