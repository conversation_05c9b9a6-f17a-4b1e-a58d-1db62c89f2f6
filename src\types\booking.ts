// Booking interface and related types
export interface Booking {
  id: number;
  title: string;
  notes: string;
  date: string;
  customer_id: number | null;
  customer_name: string;
  employee_ids: number[];
  start_time: string;
  end_time: string;
  service_ids: number[];
  package_ids: number[];
  dress_ids: number[];
  dress_rental_start_date?: string;
  dress_rental_end_date?: string;
  total_amount: number;
  deposit: number;
  discount_percentage: number;
  status: "Confirmed" | "In Progress" | "Cancelled" | "Completed";
  created_at: string;
  updated_at: string;
}

// New booking form data interface
export interface NewBookingData {
  title: string;
  notes: string;
  date: string;
  customer_id: number | null;
  customer_name: string;
  employee_ids: number[];
  start_time: string;
  end_time: string;
  service_ids: number[];
  package_ids: number[];
  dress_ids: number[];
  dress_rental_start_date: string;
  dress_rental_end_date: string;
  total_amount: number;
  manual_total: number;
  deposit: number;
  discount_percentage: number;
}

// Quick booking form data interface
export interface QuickBookingData {
  customer_name: string;
  service_type: "service" | "package" | "dress";
  service_id: string;
  date: string;
  start_time: string;
  notes: string;
  dress_rental_start_date: string;
  dress_rental_end_date: string;
}

// Customer interface (if not already defined elsewhere)
export interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  notes: string;
  created_at: string;
  updated_at: string;
}

// Service interface (if not already defined elsewhere)
export interface Service {
  id: number;
  name: string;
  description: string;
  price: number;
  duration: number;
  category: string;
  status: "Active" | "Inactive";
  created_at: string;
  updated_at: string;
}

// Package interface (if not already defined elsewhere)
export interface Package {
  id: number;
  name: string;
  description: string;
  price: number;
  service_ids: number[];
  status: "Active" | "Inactive";
  created_at: string;
  updated_at: string;
}

// Dress interface (if not already defined elsewhere)
export interface Dress {
  id: number;
  name: string;
  description: string;
  rental_price: number;
  size: string;
  color: string;
  status: "Available" | "Rented" | "Maintenance";
  created_at: string;
  updated_at: string;
}

// Employee interface (if not already defined elsewhere)
export interface Employee {
  id: number;
  name: string;
  email: string;
  phone: string;
  position: string;
  salary: number;
  status: "Active" | "Inactive";
  created_at: string;
  updated_at: string;
}

// Booking status color mapping type
export type BookingStatusColor = "default" | "secondary" | "destructive" | "outline";

// Booking status progression
export const BOOKING_STATUS_PROGRESSION: Record<string, string> = {
  "Confirmed": "In Progress",
  "In Progress": "Completed",
  "Completed": "Completed",
  "Cancelled": "Cancelled"
};

// Status color mapping
export const getStatusColor = (status: string): BookingStatusColor => {
  switch (status) {
    case "Confirmed":
      return "default";
    case "In Progress":
      return "secondary";
    case "Completed":
      return "outline";
    case "Cancelled":
      return "destructive";
    default:
      return "outline";
  }
};

// Get next status in progression
export const getNextStatus = (currentStatus: string): string => {
  return BOOKING_STATUS_PROGRESSION[currentStatus] || currentStatus;
};
