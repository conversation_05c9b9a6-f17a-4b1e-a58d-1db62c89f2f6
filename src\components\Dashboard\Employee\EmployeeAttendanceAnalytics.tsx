import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Clock, Calendar, TrendingUp, AlertTriangle } from "lucide-react";
import DatabaseManager from "@/lib/database";

interface EmployeeAttendanceAnalyticsProps {
  employeeId: number;
}

interface AttendanceAnalytics {
  lateMinutes: number;
  overtimeMinutes: number;
  absentDays: number;
  presentDays: number;
  totalWorkingDays: number;
  attendanceRate: number;
}

const EmployeeAttendanceAnalytics = ({
  employeeId,
}: EmployeeAttendanceAnalyticsProps) => {
  const [analytics, setAnalytics] = useState<{
    today: AttendanceAnalytics;
    thisWeek: AttendanceAnalytics;
    thisMonth: AttendanceAnalytics;
    thisYear: AttendanceAnalytics;
    previousWeek: AttendanceAnalytics;
    previousMonth: AttendanceAnalytics;
    previousYear: AttendanceAnalytics;
  }>({} as any);

  const dataManager = DatabaseManager.getInstance();

  useEffect(() => {
    if (employeeId) {
      calculateAnalytics();
    }
  }, [employeeId]);

  // Refresh analytics every 30 seconds to show real-time data
  useEffect(() => {
    const interval = setInterval(() => {
      if (employeeId) {
        calculateAnalytics();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [employeeId]);

  const calculateAnalytics = () => {
    const attendanceRecords = dataManager.getAttendanceByEmployee(employeeId);
    const today = new Date();

    // Add some mock data if no records exist for testing
    const mockRecords =
      attendanceRecords.length === 0
        ? [
            {
              id: 1,
              employee_id: employeeId,
              employee_name: "Test Employee",
              date: today.toISOString().split("T")[0],
              check_in: "09:00",
              check_out: "17:30",
              status: "Present" as const,
              hours: 8.5,
              late_minutes: 0,
              overtime: 0.5,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
            {
              id: 2,
              employee_id: employeeId,
              employee_name: "Test Employee",
              date: new Date(today.getTime() - 24 * 60 * 60 * 1000)
                .toISOString()
                .split("T")[0],
              check_in: "09:15",
              check_out: "17:00",
              status: "Late" as const,
              hours: 7.75,
              late_minutes: 15,
              overtime: 0,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
          ]
        : attendanceRecords;

    // Helper function to get date ranges
    const getDateRanges = () => {
      const ranges = {
        today: {
          start: new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate(),
          ),
          end: new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate() + 1,
          ),
        },
        thisWeek: {
          start: new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate() - today.getDay(),
          ),
          end: new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate() - today.getDay() + 7,
          ),
        },
        thisMonth: {
          start: new Date(today.getFullYear(), today.getMonth(), 1),
          end: new Date(today.getFullYear(), today.getMonth() + 1, 1),
        },
        thisYear: {
          start: new Date(today.getFullYear(), 0, 1),
          end: new Date(today.getFullYear() + 1, 0, 1),
        },
        previousWeek: {
          start: new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate() - today.getDay() - 7,
          ),
          end: new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate() - today.getDay(),
          ),
        },
        previousMonth: {
          start: new Date(today.getFullYear(), today.getMonth() - 1, 1),
          end: new Date(today.getFullYear(), today.getMonth(), 1),
        },
        previousYear: {
          start: new Date(today.getFullYear() - 1, 0, 1),
          end: new Date(today.getFullYear(), 0, 1),
        },
      };
      return ranges;
    };

    const ranges = getDateRanges();

    // Calculate analytics for each period
    const calculatePeriodAnalytics = (
      startDate: Date,
      endDate: Date,
    ): AttendanceAnalytics => {
      const periodRecords = mockRecords.filter((record) => {
        const recordDate = new Date(record.date + "T00:00:00");
        return recordDate >= startDate && recordDate < endDate;
      });

      const lateMinutes = periodRecords.reduce(
        (sum, record) => sum + (record.late_minutes || 0),
        0,
      );
      const overtimeMinutes = periodRecords.reduce(
        (sum, record) => sum + (record.overtime || 0) * 60,
        0,
      );
      const absentDays = periodRecords.filter(
        (record) => record.status === "Absent",
      ).length;
      const presentDays = periodRecords.filter(
        (record) => record.status === "Present" || record.status === "Late",
      ).length;

      // Calculate total working days in the period (simplified - assumes 5 working days per week)
      const totalDays = Math.ceil(
        (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
      );
      const totalWorkingDays = Math.max(1, Math.floor((totalDays * 5) / 7)); // Ensure at least 1 working day

      const attendanceRate =
        totalWorkingDays > 0
          ? Math.min(100, (presentDays / totalWorkingDays) * 100)
          : 0;

      return {
        lateMinutes,
        overtimeMinutes,
        absentDays,
        presentDays,
        totalWorkingDays,
        attendanceRate,
      };
    };

    const newAnalytics = {
      today: calculatePeriodAnalytics(ranges.today.start, ranges.today.end),
      thisWeek: calculatePeriodAnalytics(
        ranges.thisWeek.start,
        ranges.thisWeek.end,
      ),
      thisMonth: calculatePeriodAnalytics(
        ranges.thisMonth.start,
        ranges.thisMonth.end,
      ),
      thisYear: calculatePeriodAnalytics(
        ranges.thisYear.start,
        ranges.thisYear.end,
      ),
      previousWeek: calculatePeriodAnalytics(
        ranges.previousWeek.start,
        ranges.previousWeek.end,
      ),
      previousMonth: calculatePeriodAnalytics(
        ranges.previousMonth.start,
        ranges.previousMonth.end,
      ),
      previousYear: calculatePeriodAnalytics(
        ranges.previousYear.start,
        ranges.previousYear.end,
      ),
    };

    setAnalytics(newAnalytics);
  };

  const formatMinutes = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0
      ? `${hours}h ${remainingMinutes}m`
      : `${hours}h`;
  };

  const getStatusColor = (
    value: number,
    type: "late" | "overtime" | "absent" | "attendance",
  ) => {
    switch (type) {
      case "late":
        return value > 60
          ? "text-red-600"
          : value > 30
            ? "text-yellow-600"
            : "text-green-600";
      case "overtime":
        return value > 0 ? "text-blue-600" : "text-muted-foreground";
      case "absent":
        return value > 2
          ? "text-red-600"
          : value > 0
            ? "text-yellow-600"
            : "text-green-600";
      case "attendance":
        return value >= 95
          ? "text-green-600"
          : value >= 85
            ? "text-yellow-600"
            : "text-red-600";
      default:
        return "text-muted-foreground";
    }
  };

  const renderAnalyticsCard = (
    title: string,
    data: AttendanceAnalytics,
    icon: React.ReactNode,
  ) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Late Minutes:</span>
              <span
                className={`font-medium ${getStatusColor(data.lateMinutes, "late")}`}
              >
                {formatMinutes(data.lateMinutes)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Overtime:</span>
              <span
                className={`font-medium ${getStatusColor(data.overtimeMinutes, "overtime")}`}
              >
                {formatMinutes(data.overtimeMinutes)}
              </span>
            </div>
          </div>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Absent Days:</span>
              <span
                className={`font-medium ${getStatusColor(data.absentDays, "absent")}`}
              >
                {data.absentDays}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Present Days:</span>
              <span className="font-medium text-green-600">
                {data.presentDays}
              </span>
            </div>
          </div>
        </div>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Attendance Rate:</span>
            <span
              className={`font-medium ${getStatusColor(data.attendanceRate, "attendance")}`}
            >
              {data.attendanceRate.toFixed(1)}%
            </span>
          </div>
          <Progress value={data.attendanceRate} className="h-2" />
        </div>
        <div className="text-xs text-muted-foreground">
          {data.presentDays} of {data.totalWorkingDays} working days
        </div>
      </CardContent>
    </Card>
  );

  if (!analytics.today) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Loading attendance analytics...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Calendar className="h-5 w-5" />
        <h3 className="text-lg font-semibold">Attendance Analytics</h3>
      </div>
      <Tabs defaultValue="current" className="space-y-4">
        <TabsList>
          <TabsTrigger value="current">Current Period</TabsTrigger>
          <TabsTrigger value="previous">Previous Period</TabsTrigger>
          <TabsTrigger value="comparison">Comparison</TabsTrigger>
        </TabsList>

        <TabsContent value="current" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-4">
            {renderAnalyticsCard(
              "Today",
              analytics.today,
              <Clock className="h-4 w-4 text-muted-foreground" />,
            )}
            {renderAnalyticsCard(
              "This Week",
              analytics.thisWeek,
              <Calendar className="h-4 w-4 text-muted-foreground" />,
            )}
            {renderAnalyticsCard(
              "This Month",
              analytics.thisMonth,
              <TrendingUp className="h-4 w-4 text-muted-foreground" />,
            )}
            {renderAnalyticsCard(
              "This Year",
              analytics.thisYear,
              <Calendar className="h-4 w-4 text-muted-foreground" />,
            )}
          </div>
        </TabsContent>
        <TabsContent value="previous" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-3">
            {renderAnalyticsCard(
              "Previous Week",
              analytics.previousWeek,
              <Calendar className="h-4 w-4 text-muted-foreground" />,
            )}
            {renderAnalyticsCard(
              "Previous Month",
              analytics.previousMonth,
              <TrendingUp className="h-4 w-4 text-muted-foreground" />,
            )}
            {renderAnalyticsCard(
              "Previous Year",
              analytics.previousYear,
              <Calendar className="h-4 w-4 text-muted-foreground" />,
            )}
          </div>
        </TabsContent>
        <TabsContent value="comparison" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Week Comparison</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Late Minutes
                    </span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">
                        This: {formatMinutes(analytics.thisWeek.lateMinutes)}
                      </span>
                      <span className="text-sm text-muted-foreground">vs</span>
                      <span className="text-sm">
                        Prev:{" "}
                        {formatMinutes(analytics.previousWeek.lateMinutes)}
                      </span>
                      {analytics.thisWeek.lateMinutes <
                      analytics.previousWeek.lateMinutes ? (
                        <Badge variant="outline" className="text-green-600">
                          ↓ Better
                        </Badge>
                      ) : analytics.thisWeek.lateMinutes >
                        analytics.previousWeek.lateMinutes ? (
                        <Badge variant="outline" className="text-red-600">
                          ↑ Worse
                        </Badge>
                      ) : (
                        <Badge variant="outline">→ Same</Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Attendance Rate
                    </span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">
                        {analytics.thisWeek.attendanceRate.toFixed(1)}%
                      </span>
                      <span className="text-sm text-muted-foreground">vs</span>
                      <span className="text-sm">
                        {analytics.previousWeek.attendanceRate.toFixed(1)}%
                      </span>
                      {analytics.thisWeek.attendanceRate >
                      analytics.previousWeek.attendanceRate ? (
                        <Badge variant="outline" className="text-green-600">
                          ↑ Better
                        </Badge>
                      ) : analytics.thisWeek.attendanceRate <
                        analytics.previousWeek.attendanceRate ? (
                        <Badge variant="outline" className="text-red-600">
                          ↓ Worse
                        </Badge>
                      ) : (
                        <Badge variant="outline">→ Same</Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Month Comparison</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Absent Days
                    </span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">
                        This: {analytics.thisMonth.absentDays}
                      </span>
                      <span className="text-sm text-muted-foreground">vs</span>
                      <span className="text-sm">
                        Prev: {analytics.previousMonth.absentDays}
                      </span>
                      {analytics.thisMonth.absentDays <
                      analytics.previousMonth.absentDays ? (
                        <Badge variant="outline" className="text-green-600">
                          ↓ Better
                        </Badge>
                      ) : analytics.thisMonth.absentDays >
                        analytics.previousMonth.absentDays ? (
                        <Badge variant="outline" className="text-red-600">
                          ↑ Worse
                        </Badge>
                      ) : (
                        <Badge variant="outline">→ Same</Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Overtime
                    </span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">
                        {formatMinutes(analytics.thisMonth.overtimeMinutes)}
                      </span>
                      <span className="text-sm text-muted-foreground">vs</span>
                      <span className="text-sm">
                        {formatMinutes(analytics.previousMonth.overtimeMinutes)}
                      </span>
                      {analytics.thisMonth.overtimeMinutes >
                      analytics.previousMonth.overtimeMinutes ? (
                        <Badge variant="outline" className="text-blue-600">
                          ↑ More
                        </Badge>
                      ) : analytics.thisMonth.overtimeMinutes <
                        analytics.previousMonth.overtimeMinutes ? (
                        <Badge
                          variant="outline"
                          className="text-muted-foreground"
                        >
                          ↓ Less
                        </Badge>
                      ) : (
                        <Badge variant="outline">→ Same</Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Performance Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            {analytics.thisMonth.attendanceRate >= 95 && (
              <div className="flex items-center space-x-2 text-green-600">
                <div className="w-2 h-2 rounded-full bg-green-600" />
                <span>Excellent attendance rate this month!</span>
              </div>
            )}
            {analytics.thisMonth.lateMinutes > 120 && (
              <div className="flex items-center space-x-2 text-yellow-600">
                <div className="w-2 h-2 rounded-full bg-yellow-600" />
                <span>
                  High late minutes this month - consider discussing punctuality
                </span>
              </div>
            )}
            {analytics.thisMonth.overtimeMinutes > 600 && (
              <div className="flex items-center space-x-2 text-blue-600">
                <div className="w-2 h-2 rounded-full bg-blue-600" />
                <span>
                  Significant overtime this month - eligible for bonus
                </span>
              </div>
            )}
            {analytics.thisMonth.absentDays > 3 && (
              <div className="flex items-center space-x-2 text-red-600">
                <div className="w-2 h-2 rounded-full bg-red-600" />
                <span>High absence count - may require attention</span>
              </div>
            )}
            {analytics.thisMonth.attendanceRate >= 85 &&
              analytics.thisMonth.attendanceRate < 95 && (
                <div className="flex items-center space-x-2 text-yellow-600">
                  <div className="w-2 h-2 rounded-full bg-yellow-600" />
                  <span>Good attendance but room for improvement</span>
                </div>
              )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EmployeeAttendanceAnalytics;
