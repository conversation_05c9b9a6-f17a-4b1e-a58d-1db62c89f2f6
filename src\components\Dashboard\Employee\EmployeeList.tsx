import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface EmployeeListProps {
  employees: any[];
  onViewProfile: (employee: any) => void;
  showDepartment?: boolean;
  showLeaveInfo?: boolean;
}

const EmployeeList = ({
  employees,
  onViewProfile,
  showDepartment = false,
  showLeaveInfo = false,
}: EmployeeListProps) => {
  const getTableHeaders = () => {
    if (showLeaveInfo) {
      return (
        <TableRow>
          <TableHead>Employee</TableHead>
          <TableHead>Position</TableHead>
          <TableHead>Department</TableHead>
          <TableHead>Leave Reason</TableHead>
          <TableHead>Expected Return</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      );
    }

    if (showDepartment) {
      return (
        <TableRow>
          <TableHead>Employee</TableHead>
          <TableHead>Position</TableHead>
          <TableHead>Department</TableHead>
          <TableHead>Attendance</TableHead>
          <TableHead>Salary</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      );
    }

    return (
      <TableRow>
        <TableHead>Employee</TableHead>
        <TableHead>Position</TableHead>
        <TableHead>Status</TableHead>
        <TableHead>Attendance</TableHead>
        <TableHead>Salary</TableHead>
        <TableHead>Last Check-in</TableHead>
        <TableHead>Actions</TableHead>
      </TableRow>
    );
  };

  const renderTableRow = (employee: any) => {
    if (showLeaveInfo) {
      return (
        <TableRow key={employee.id}>
          <TableCell>
            <div className="flex items-center space-x-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={employee.avatar} alt={employee.name} />
                <AvatarFallback>
                  {employee.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium">{employee.name}</div>
                <div className="text-sm text-muted-foreground">
                  {employee.email}
                </div>
              </div>
            </div>
          </TableCell>
          <TableCell>{employee.position}</TableCell>
          <TableCell className="text-sm text-muted-foreground">
            Medical Leave
          </TableCell>
          <TableCell className="text-sm text-muted-foreground">
            Jan 20, 2024
          </TableCell>
          <TableCell>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                Contact
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewProfile(employee)}
              >
                View Profile
              </Button>
            </div>
          </TableCell>
        </TableRow>
      );
    }

    if (showDepartment) {
      return (
        <TableRow key={employee.id}>
          <TableCell>
            <div className="flex items-center space-x-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={employee.avatar} alt={employee.name} />
                <AvatarFallback>
                  {employee.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium">{employee.name}</div>
                <div className="text-sm text-muted-foreground">
                  {employee.email}
                </div>
              </div>
            </div>
          </TableCell>
          <TableCell>{employee.position}</TableCell>
          <TableCell>
            <div className="flex items-center space-x-2">
              <Progress value={employee.attendance} className="h-2 w-16" />
              <span className="text-sm font-medium">
                {employee.attendance}%
              </span>
            </div>
          </TableCell>
          <TableCell className="font-medium">
            ${employee.salary.toLocaleString()}
          </TableCell>
          <TableCell>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewProfile(employee)}
              >
                View Profile
              </Button>
            </div>
          </TableCell>
        </TableRow>
      );
    }

    return (
      <TableRow key={employee.id}>
        <TableCell>
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={employee.avatar} alt={employee.name} />
              <AvatarFallback>
                {employee.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{employee.name}</div>
              <div className="text-sm text-muted-foreground">
                {employee.email}
              </div>
            </div>
          </div>
        </TableCell>
        <TableCell>{employee.position}</TableCell>
        <TableCell>
          <Badge
            variant={employee.status === "Active" ? "default" : "secondary"}
          >
            {employee.status}
          </Badge>
        </TableCell>
        <TableCell>
          <div className="flex items-center space-x-2">
            <Progress value={employee.attendance} className="h-2 w-16" />
            <span className="text-sm font-medium">{employee.attendance}%</span>
          </div>
        </TableCell>
        <TableCell className="font-medium">
          ${employee.salary.toLocaleString()}
        </TableCell>
        <TableCell className="text-sm text-muted-foreground">
          {employee.last_attendance}
        </TableCell>
        <TableCell>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewProfile(employee)}
            >
              View Profile
            </Button>
          </div>
        </TableCell>
      </TableRow>
    );
  };

  return (
    <Card>
      <CardContent className="p-0">
        <Table>
          <TableHeader>{getTableHeaders()}</TableHeader>
          <TableBody>{employees.map(renderTableRow)}</TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default EmployeeList;
