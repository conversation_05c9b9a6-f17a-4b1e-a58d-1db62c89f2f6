import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { DollarSign } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface EmployeeAdvancesProps {
  employees: any[];
  advances: any[];
  dataManager: any;
  showAdvanceDialog: boolean;
  setShowAdvanceDialog: (show: boolean) => void;
  selectedEmployeeForAdvance: string;
  setSelectedEmployeeForAdvance: (id: string) => void;
  advanceAmount: number;
  setAdvanceAmount: (amount: number) => void;
  advanceReason: string;
  setAdvanceReason: (reason: string) => void;
  onAdvanceRequest: () => void;
  onRefreshData: () => void;
}

const EmployeeAdvances = ({
  employees,
  advances,
  dataManager,
  showAdvanceDialog,
  setShowAdvanceDialog,
  selectedEmployeeForAdvance,
  setSelectedEmployeeForAdvance,
  advanceAmount,
  setAdvanceAmount,
  advanceReason,
  setAdvanceReason,
  onAdvanceRequest,
  onRefreshData,
}: EmployeeAdvancesProps) => {
  return (
    <>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Employee Advances</h3>
          <p className="text-sm text-muted-foreground">
            {dataManager.isFirstWeekOfMonth()
              ? "Advances are not allowed during the first week of the month"
              : "Manage employee salary advances"}
          </p>
        </div>
        <Button
          onClick={() => setShowAdvanceDialog(true)}
          disabled={dataManager.isFirstWeekOfMonth()}
        >
          <DollarSign className="mr-2 h-4 w-4" />
          Request Advance
        </Button>
      </div>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employee</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Reason</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {advances
                .filter((advance) => advance.status === "active")
                .sort(
                  (a, b) =>
                    new Date(b.created_at).getTime() -
                    new Date(a.created_at).getTime(),
                )
                .map((advance) => (
                  <TableRow key={advance.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center">
                          <span className="text-xs font-medium">
                            {advance.employee_name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </span>
                        </div>
                        <span className="font-medium">
                          {advance.employee_name}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium text-red-600">
                      -${advance.amount.toLocaleString()}
                    </TableCell>
                    <TableCell className="text-sm">{advance.reason}</TableCell>
                    <TableCell className="text-sm">
                      {new Date(advance.date).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">Active</Badge>
                    </TableCell>
                  </TableRow>
                ))}
              {advances.filter((advance) => advance.status === "active")
                .length === 0 && (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    className="text-center py-8 text-muted-foreground"
                  >
                    No active advances found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Current Month Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Current Month Advances Summary</CardTitle>
          <CardDescription>
            {new Date().toLocaleDateString("en-US", {
              month: "long",
              year: "numeric",
            })}{" "}
            advances overview
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {employees
              .filter((emp) => emp.status === "Active")
              .map((employee) => {
                const currentMonth = new Date().toISOString().slice(0, 7);
                const employeeAdvances = advances.filter(
                  (adv) =>
                    adv.employee_id === employee.id &&
                    adv.month === currentMonth &&
                    adv.status === "active",
                );
                const totalAdvances = employeeAdvances.reduce(
                  (sum, adv) => sum + adv.amount,
                  0,
                );

                return (
                  <div key={employee.id} className="p-4 border rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <span className="text-sm font-medium">
                          {employee.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-sm">
                          {employee.name}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {employee.position}
                        </div>
                      </div>
                    </div>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          Base Salary:
                        </span>
                        <span className="font-medium">
                          ${employee.salary.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Advances:</span>
                        <span
                          className={`font-medium ${totalAdvances > 0 ? "text-red-600" : "text-green-600"}`}
                        >
                          {totalAdvances > 0
                            ? `-${totalAdvances.toLocaleString()}`
                            : "$0"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Count:</span>
                        <span className="font-medium">
                          {employeeAdvances.length} advances
                        </span>
                      </div>
                      <Progress
                        value={Math.min(
                          (totalAdvances / employee.salary) * 100,
                          100,
                        )}
                        className="h-1 mt-2"
                      />
                    </div>
                  </div>
                );
              })}
          </div>
        </CardContent>
      </Card>

      {/* Advance Request Dialog */}
      <Dialog open={showAdvanceDialog} onOpenChange={setShowAdvanceDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Request Employee Advance</DialogTitle>
            <DialogDescription>
              Process a salary advance for an employee (not allowed during first
              week of month)
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="employee-advance-select">Select Employee</Label>
              <Select
                value={selectedEmployeeForAdvance}
                onValueChange={setSelectedEmployeeForAdvance}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose employee" />
                </SelectTrigger>
                <SelectContent>
                  {employees
                    .filter((emp) => emp.status === "Active")
                    .map((employee) => (
                      <SelectItem
                        key={employee.id}
                        value={employee.id.toString()}
                      >
                        {employee.name} - ${employee.salary.toLocaleString()}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="advance-amount">Advance Amount ($)</Label>
              <Input
                id="advance-amount"
                type="number"
                min="1"
                step="0.01"
                placeholder="Enter amount"
                value={advanceAmount || ""}
                onChange={(e) =>
                  setAdvanceAmount(parseFloat(e.target.value) || 0)
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="advance-reason">Reason for Advance</Label>
              <Textarea
                id="advance-reason"
                placeholder="Enter reason for advance request..."
                value={advanceReason}
                onChange={(e) => setAdvanceReason(e.target.value)}
              />
            </div>
            {dataManager.isFirstWeekOfMonth() && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  ⚠️ Advances are not allowed during the first week of the month
                  (1st-7th).
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAdvanceDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={onAdvanceRequest}
              disabled={
                !selectedEmployeeForAdvance ||
                advanceAmount <= 0 ||
                !advanceReason ||
                dataManager.isFirstWeekOfMonth()
              }
            >
              Process Advance
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EmployeeAdvances;
