import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  CalendarIcon,
  Clock,
  Search,
  Plus,
  Trash2,
  Phone,
  Mail,
  MapPin,
  User,
  Eye,
  Edit,
  Star,
} from "lucide-react";
import {
  Booking,
  NewBookingData,
  Customer,
  Service,
  Package,
  Dress,
  Employee,
  getStatusColor,
  getNextStatus,
} from "@/types/booking";
import DatabaseManager from "@/lib/database";

const BookingManagement: React.FC = () => {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [showNewBookingDialog, setShowNewBookingDialog] = useState(false);
  const [services, setServices] = useState<Service[]>([]);
  const [packages, setPackages] = useState<Package[]>([]);
  const [dresses, setDresses] = useState<Dress[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [customerSearchTerm, setCustomerSearchTerm] = useState("");
  const [showAddCustomer, setShowAddCustomer] = useState(false);
  const [manualPriceOverride, setManualPriceOverride] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [showBookingDetails, setShowBookingDetails] = useState(false);
  const [editingBooking, setEditingBooking] = useState<Booking | null>(null);

  const [newBooking, setNewBooking] = useState<NewBookingData>({
    title: "",
    notes: "",
    date: "",
    customer_id: null,
    customer_name: "",
    employee_ids: [],
    start_time: "",
    end_time: "",
    service_ids: [],
    package_ids: [],
    dress_ids: [],
    dress_rental_start_date: "",
    dress_rental_end_date: "",
    total_amount: 0,
    manual_total: 0,
    deposit: 0,
    discount_percentage: 0,
  });

  const dataManager = DatabaseManager.getInstance();

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    // Auto-select date when calendar date changes
    if (date) {
      const localDate = new Date(
        date.getTime() - date.getTimezoneOffset() * 60000,
      );
      setNewBooking((prev) => ({
        ...prev,
        date: localDate.toISOString().split("T")[0],
      }));
    }
  }, [date]);

  useEffect(() => {
    // Calculate total amount when selections change
    calculateTotalAmount();
  }, [
    newBooking.service_ids,
    newBooking.package_ids,
    newBooking.dress_ids,
    newBooking.discount_percentage,
  ]);

  const loadData = () => {
    setServices(dataManager.getServices());
    setPackages(dataManager.getPackages());
    setDresses(
      dataManager.getDresses().filter((dress) => dress.status === "Available"),
    );
    setEmployees(
      dataManager.getEmployees().filter((emp) => emp.status === "Active"),
    );
    setCustomers(dataManager.getCustomers());

    // Load existing bookings from database
    const dbBookings = dataManager.getBookings().map((booking) => ({
      ...booking,
      service_ids: JSON.parse(booking.service_ids || "[]"),
      package_ids: JSON.parse(booking.package_ids || "[]"),
      dress_ids: JSON.parse(booking.dress_ids || "[]"),
      employee_ids: JSON.parse(booking.employee_ids || "[]"),
    }));
    setBookings(dbBookings);
  };

  const calculateTotalAmount = () => {
    if (manualPriceOverride) return;

    let total = 0;

    // Add service prices
    newBooking.service_ids.forEach((serviceId) => {
      const service = services.find((s) => s.id === serviceId);
      if (service) total += service.price;
    });

    // Add package prices
    newBooking.package_ids.forEach((packageId) => {
      const pkg = packages.find((p) => p.id === packageId);
      if (pkg) total += pkg.price;
    });

    // Add dress rental prices
    newBooking.dress_ids.forEach((dressId) => {
      const dress = dresses.find((d) => d.id === dressId);
      if (dress) total += dress.rental_price;
    });

    // Apply discount
    const discountAmount = (total * newBooking.discount_percentage) / 100;
    total -= discountAmount;

    setNewBooking((prev) => ({
      ...prev,
      total_amount: Math.max(0, total),
    }));
  };

  // Handler functions
  const handleServiceToggle = (serviceId: number) => {
    setNewBooking((prev) => ({
      ...prev,
      service_ids: prev.service_ids.includes(serviceId)
        ? prev.service_ids.filter((id) => id !== serviceId)
        : [...prev.service_ids, serviceId],
    }));
  };

  const handlePackageToggle = (packageId: number) => {
    setNewBooking((prev) => ({
      ...prev,
      package_ids: prev.package_ids.includes(packageId)
        ? prev.package_ids.filter((id) => id !== packageId)
        : [...prev.package_ids, packageId],
    }));
  };

  const handleDressToggle = (dressId: number) => {
    setNewBooking((prev) => {
      const newDressIds = prev.dress_ids.includes(dressId)
        ? prev.dress_ids.filter((id) => id !== dressId)
        : [...prev.dress_ids, dressId];

      // If no dresses are selected, clear rental dates
      const rentalDates =
        newDressIds.length === 0
          ? { dress_rental_start_date: "", dress_rental_end_date: "" }
          : {
              dress_rental_start_date: prev.dress_rental_start_date,
              dress_rental_end_date: prev.dress_rental_end_date,
            };

      return {
        ...prev,
        dress_ids: newDressIds,
        ...rentalDates,
      };
    });
  };

  const handleEmployeeToggle = (employeeId: number) => {
    setNewBooking((prev) => ({
      ...prev,
      employee_ids: prev.employee_ids.includes(employeeId)
        ? prev.employee_ids.filter((id) => id !== employeeId)
        : [...prev.employee_ids, employeeId],
    }));
  };

  const handleBookingClick = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowBookingDetails(true);
  };

  const handleStatusChange = (bookingId: number, newStatus: string) => {
    const success = dataManager.updateBookingStatus(bookingId, newStatus);
    if (success) {
      loadData();
    }
  };

  // Get bookings for selected date
  const dayBookings = bookings.filter((booking) => {
    if (!date) return false;
    const bookingDate = new Date(booking.date);
    return (
      bookingDate.getFullYear() === date.getFullYear() &&
      bookingDate.getMonth() === date.getMonth() &&
      bookingDate.getDate() === date.getDate()
    );
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">
          Booking Management
        </h2>
        <Button
          onClick={() => {
            // Reset form when opening new booking dialog
            setNewBooking({
              title: "",
              notes: "",
              date: date
                ? new Date(date.getTime() - date.getTimezoneOffset() * 60000)
                    .toISOString()
                    .split("T")[0]
                : "",
              customer_id: null,
              customer_name: "",
              employee_ids: [],
              start_time: "",
              end_time: "",
              service_ids: [],
              package_ids: [],
              dress_ids: [],
              dress_rental_start_date: "",
              dress_rental_end_date: "",
              total_amount: 0,
              manual_total: 0,
              deposit: 0,
              discount_percentage: 0,
            });
            setCustomerSearchTerm("");
            setManualPriceOverride(false);
            setEditingBooking(null);
            setShowNewBookingDialog(true);
          }}
        >
          <Plus className="mr-2 h-4 w-4" />
          New Booking
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Select Date</CardTitle>
            <CardDescription>Choose a date to view bookings</CardDescription>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={date}
              onSelect={setDate}
              className="rounded-md border"
            />
          </CardContent>
        </Card>

        <Card className="col-span-2">
          <CardHeader>
            <CardTitle>Bookings for {date?.toLocaleDateString()}</CardTitle>
            <CardDescription>
              Manage appointments and services ({dayBookings.length} bookings)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dayBookings.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <CalendarIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No bookings for this date</p>
                  <p className="text-sm">Click "New Booking" to create one</p>
                </div>
              ) : (
                dayBookings
                  .sort((a, b) => a.start_time.localeCompare(b.start_time))
                  .map((booking) => {
                    const employeeNames =
                      booking.employee_ids
                        .map(
                          (id) => employees.find((emp) => emp.id === id)?.name,
                        )
                        .filter(Boolean)
                        .join(", ") || "No staff assigned";

                    const serviceNames = [
                      ...booking.service_ids.map(
                        (id) => services.find((s) => s.id === id)?.name,
                      ),
                      ...booking.package_ids.map(
                        (id) => packages.find((p) => p.id === id)?.name,
                      ),
                      ...booking.dress_ids.map(
                        (id) => dresses.find((d) => d.id === id)?.name,
                      ),
                    ]
                      .filter(Boolean)
                      .join(", ");

                    const currentStatus = booking.status;

                    return (
                      <div
                        key={booking.id}
                        className="flex items-start space-x-4 p-3 rounded-lg border cursor-pointer hover:bg-muted/50 transition-colors"
                        onClick={() => handleBookingClick(booking)}
                      >
                        <div className="bg-primary/10 p-2 rounded-md">
                          <Clock className="h-5 w-5 text-primary" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">
                              {booking.start_time
                                ? `${booking.start_time}${booking.end_time ? ` - ${booking.end_time}` : ""}`
                                : "Time not set"}
                            </h4>
                            <Badge
                              variant={getStatusColor(currentStatus)}
                              className="cursor-pointer hover:opacity-80"
                              onClick={(e) => {
                                e.stopPropagation();
                                if (currentStatus !== "Cancelled") {
                                  handleStatusChange(
                                    booking.id,
                                    getNextStatus(currentStatus),
                                  );
                                }
                              }}
                            >
                              {currentStatus}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {booking.customer_name}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {serviceNames || "No services"}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Staff: {employeeNames}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-sm font-medium">
                              ${booking.total_amount}
                            </span>
                            <div className="flex space-x-1">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditBooking(booking);
                                }}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (
                                    confirm(
                                      "Are you sure you want to delete this booking?",
                                    )
                                  ) {
                                    const success = dataManager.deleteBooking(
                                      booking.id,
                                    );
                                    if (success) {
                                      loadData();
                                    }
                                  }
                                }}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Booking Details Dialog */}
      <Dialog open={showBookingDetails} onOpenChange={setShowBookingDetails}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Booking Details</DialogTitle>
          </DialogHeader>
          {selectedBooking && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Customer</Label>
                  <p className="text-sm">{selectedBooking.customer_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Date & Time</Label>
                  <p className="text-sm">
                    {new Date(selectedBooking.date).toLocaleDateString()}
                    {selectedBooking.start_time && ` at ${selectedBooking.start_time}`}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Status</Label>
                  <Badge variant={getStatusColor(selectedBooking.status)}>
                    {selectedBooking.status}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium">Total Amount</Label>
                  <p className="text-sm font-bold">${selectedBooking.total_amount}</p>
                </div>
              </div>
              {selectedBooking.notes && (
                <div>
                  <Label className="text-sm font-medium">Notes</Label>
                  <p className="text-sm">{selectedBooking.notes}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

const handleEditBooking = (booking: Booking) => {
  // Get the database booking to access rental dates
  const dbBooking = dataManager.getBookings().find((b) => b.id === booking.id);

  setEditingBooking(booking);
  setNewBooking({
    title: booking.title,
    notes: booking.notes,
    date: booking.date,
    customer_id: booking.customer_id,
    customer_name: booking.customer_name,
    employee_ids: booking.employee_ids,
    start_time: booking.start_time,
    end_time: booking.end_time,
    service_ids: booking.service_ids,
    package_ids: booking.package_ids,
    dress_ids: booking.dress_ids,
    dress_rental_start_date: dbBooking?.dress_rental_start_date || "",
    dress_rental_end_date: dbBooking?.dress_rental_end_date || "",
    total_amount: booking.total_amount,
    manual_total: booking.total_amount,
    deposit: booking.deposit,
    discount_percentage: booking.discount_percentage,
  });
  setCustomerSearchTerm(booking.customer_name);
  setShowNewBookingDialog(true);
};

export default BookingManagement;
