import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import DatabaseManager from "@/lib/database";

interface AttendanceDialogsProps {
  showMarkAttendanceDialog: boolean;
  setShowMarkAttendanceDialog: (show: boolean) => void;
  showBulkCheckinDialog: boolean;
  setShowBulkCheckinDialog: (show: boolean) => void;
  showLatePenaltyDialog: boolean;
  setShowLatePenaltyDialog: (show: boolean) => void;
  showEditDialog: boolean;
  setShowEditDialog: (show: boolean) => void;
  editingRecord: any;
  setEditingRecord: (record: any) => void;
  employees: any[];
  attendanceRecords: any[];
  settings: any;
  selectedDate: Date | undefined;
  onRefreshData: () => void;
}

const AttendanceDialogs = ({
  showMarkAttendanceDialog,
  setShowMarkAttendanceDialog,
  showBulkCheckinDialog,
  setShowBulkCheckinDialog,
  showLatePenaltyDialog,
  setShowLatePenaltyDialog,
  showEditDialog,
  setShowEditDialog,
  editingRecord,
  setEditingRecord,
  employees,
  attendanceRecords,
  settings,
  selectedDate,
  onRefreshData,
}: AttendanceDialogsProps) => {
  const [selectedEmployeeForAttendance, setSelectedEmployeeForAttendance] =
    useState("");
  const [checkInTime, setCheckInTime] = useState("");
  const [checkOutTime, setCheckOutTime] = useState("");
  const [selectedEmployeesForBulk, setSelectedEmployeesForBulk] = useState<
    number[]
  >([]);
  const [selectedEmployeesForPenalty, setSelectedEmployeesForPenalty] =
    useState<number[]>([]);
  const [penaltyReason, setPenaltyReason] = useState("");
  const [penaltyAmount, setPenaltyAmount] = useState(0);
  const [editCheckInTime, setEditCheckInTime] = useState("");
  const [editCheckOutTime, setEditCheckOutTime] = useState("");
  const [editNotes, setEditNotes] = useState("");

  const dataManager = DatabaseManager.getInstance();

  // Utility functions
  const getCurrentTime = () => {
    const now = new Date();
    return now.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const calculateLateMinutes = (
    checkInTime: string,
    startTime: string,
    allowanceTime: number,
  ) => {
    const checkIn = new Date(`2000-01-01 ${checkInTime}`);
    const start = new Date(`2000-01-01 ${startTime}`);
    const allowance = allowanceTime * 60 * 1000;

    const diff = checkIn.getTime() - start.getTime() - allowance;
    return diff > 0 ? Math.floor(diff / (60 * 1000)) : 0;
  };

  const calculateHours = (checkIn: string, checkOut: string) => {
    const start = new Date(`2000-01-01 ${checkIn}`);
    const end = new Date(`2000-01-01 ${checkOut}`);
    const diff = end.getTime() - start.getTime();
    return Math.round((diff / (1000 * 60 * 60)) * 100) / 100;
  };

  const formatDateForComparison = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  // Mark Attendance Handler
  const markAttendance = () => {
    if (!selectedEmployeeForAttendance || !checkInTime) return;

    const employee = employees.find(
      (emp) => emp.id.toString() === selectedEmployeeForAttendance,
    );
    if (!employee) return;

    const attendanceDate = selectedDate
      ? formatDateForComparison(selectedDate)
      : formatDateForComparison(new Date());

    const lateMinutes = calculateLateMinutes(
      checkInTime,
      settings.workingHours?.startTime || "09:00",
      settings.attendanceRules?.allowanceTime || 15,
    );

    const hours = checkOutTime ? calculateHours(checkInTime, checkOutTime) : 0;
    const standardHours = 8;
    const overtime = hours > standardHours ? hours - standardHours : 0;

    const newRecord = {
      employee_id: employee.id,
      employee_name: employee.name,
      date: attendanceDate,
      check_in: checkInTime,
      check_out: checkOutTime || "",
      status: (lateMinutes > 0 ? "Late" : "Present") as
        | "Present"
        | "Late"
        | "Absent",
      hours: hours,
      late_minutes: lateMinutes,
      overtime: overtime,
    };

    dataManager.addAttendanceRecord(newRecord);
    onRefreshData();
    setShowMarkAttendanceDialog(false);
    setSelectedEmployeeForAttendance("");
    setCheckInTime("");
    setCheckOutTime("");
  };

  // Bulk Check-in Handler
  const bulkCheckin = () => {
    if (selectedEmployeesForBulk.length === 0) return;

    const today = new Date().toISOString().split("T")[0];
    const currentTime = getCurrentTime();
    const lateMinutes = calculateLateMinutes(
      currentTime,
      settings.workingHours?.startTime || "09:00",
      settings.attendanceRules?.allowanceTime || 15,
    );

    selectedEmployeesForBulk.forEach((employeeId) => {
      const employee = employees.find((emp) => emp.id === employeeId);
      if (employee) {
        const newRecord = {
          employee_id: employee.id,
          employee_name: employee.name,
          date: today,
          check_in: currentTime,
          check_out: "",
          status: (lateMinutes > 0 ? "Late" : "Present") as
            | "Present"
            | "Late"
            | "Absent",
          hours: 0,
          late_minutes: lateMinutes,
          overtime: 0,
        };
        dataManager.addAttendanceRecord(newRecord);
      }
    });

    onRefreshData();
    setShowBulkCheckinDialog(false);
    setSelectedEmployeesForBulk([]);
  };

  // Late Penalty Handler
  const markLatePenalty = () => {
    if (selectedEmployeesForPenalty.length === 0 || !penaltyReason) return;

    selectedEmployeesForPenalty.forEach((employeeId) => {
      const employee = employees.find((emp) => emp.id === employeeId);
      if (employee) {
        const penalty = {
          employee_id: employee.id,
          employee_name: employee.name,
          reason: penaltyReason,
          amount:
            penaltyAmount || settings.attendanceRules?.lateArrivalPenalty || 50,
          date: new Date().toISOString().split("T")[0],
          type: "Late Penalty",
        };
        dataManager.addPenalty(penalty);
      }
    });

    setShowLatePenaltyDialog(false);
    setSelectedEmployeesForPenalty([]);
    setPenaltyReason("");
    setPenaltyAmount(0);
  };

  // Edit Record Handler
  const saveEditedRecord = () => {
    if (!editingRecord || !editCheckInTime) return;

    const lateMinutes = calculateLateMinutes(
      editCheckInTime,
      settings.workingHours?.startTime || "09:00",
      settings.attendanceRules?.allowanceTime || 15,
    );
    const hours = editCheckOutTime
      ? calculateHours(editCheckInTime, editCheckOutTime)
      : 0;
    const standardHours = 8;
    const overtime = hours > standardHours ? hours - standardHours : 0;

    const updatedRecord = {
      check_in: editCheckInTime,
      check_out: editCheckOutTime,
      status: (lateMinutes > 0 ? "Late" : "Present") as
        | "Present"
        | "Late"
        | "Absent",
      hours: hours,
      late_minutes: lateMinutes,
      overtime: overtime,
      notes: editNotes,
    };

    dataManager.updateAttendanceRecord(editingRecord.id, updatedRecord);
    onRefreshData();
    setShowEditDialog(false);
    setEditingRecord(null);
    setEditCheckInTime("");
    setEditCheckOutTime("");
    setEditNotes("");
  };

  // Initialize edit dialog when record is selected
  React.useEffect(() => {
    if (editingRecord) {
      setEditCheckInTime(editingRecord.check_in);
      setEditCheckOutTime(editingRecord.check_out || "");
      setEditNotes(editingRecord.notes || "");
    }
  }, [editingRecord]);

  return (
    <>
      {/* Mark Attendance Dialog */}
      <Dialog
        open={showMarkAttendanceDialog}
        onOpenChange={setShowMarkAttendanceDialog}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Mark Attendance</DialogTitle>
            <DialogDescription>
              Record attendance for an employee
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="employee-select">Select Employee</Label>
              <Select
                value={selectedEmployeeForAttendance}
                onValueChange={setSelectedEmployeeForAttendance}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose employee" />
                </SelectTrigger>
                <SelectContent>
                  {employees
                    .filter((emp) => emp.status === "Active")
                    .map((employee) => (
                      <SelectItem
                        key={employee.id}
                        value={employee.id.toString()}
                      >
                        {employee.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="checkin-time">Check-in Time</Label>
                <Input
                  id="checkin-time"
                  type="time"
                  value={checkInTime}
                  onChange={(e) => setCheckInTime(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="checkout-time">Check-out Time (Optional)</Label>
                <Input
                  id="checkout-time"
                  type="time"
                  value={checkOutTime}
                  onChange={(e) => setCheckOutTime(e.target.value)}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowMarkAttendanceDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={markAttendance}>Mark Attendance</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Check-in Dialog */}
      <Dialog
        open={showBulkCheckinDialog}
        onOpenChange={setShowBulkCheckinDialog}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bulk Check-in</DialogTitle>
            <DialogDescription>
              Check-in multiple employees at once with current time
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Select Employees</Label>
              <div className="max-h-48 overflow-y-auto space-y-2">
                {employees
                  .filter((emp) => emp.status === "Active")
                  .map((employee) => (
                    <div
                      key={employee.id}
                      className="flex items-center space-x-2"
                    >
                      <input
                        type="checkbox"
                        id={`bulk-${employee.id}`}
                        checked={selectedEmployeesForBulk.includes(employee.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedEmployeesForBulk([
                              ...selectedEmployeesForBulk,
                              employee.id,
                            ]);
                          } else {
                            setSelectedEmployeesForBulk(
                              selectedEmployeesForBulk.filter(
                                (id) => id !== employee.id,
                              ),
                            );
                          }
                        }}
                        className="rounded"
                      />
                      <Label
                        htmlFor={`bulk-${employee.id}`}
                        className="text-sm"
                      >
                        {employee.name} - {employee.position}
                      </Label>
                    </div>
                  ))}
              </div>
            </div>
            <div className="p-3 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground">
                Current time: {getCurrentTime()}
              </p>
              <p className="text-sm text-muted-foreground">
                Selected: {selectedEmployeesForBulk.length} employees
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowBulkCheckinDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={bulkCheckin}
              disabled={selectedEmployeesForBulk.length === 0}
            >
              Check-in All
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Late Penalty Dialog */}
      <Dialog
        open={showLatePenaltyDialog}
        onOpenChange={setShowLatePenaltyDialog}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Mark Late Penalty</DialogTitle>
            <DialogDescription>
              Apply penalty to employees for late arrivals or absences
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Select Employees</Label>
              <div className="max-h-48 overflow-y-auto space-y-2">
                {employees
                  .filter((emp) => emp.status === "Active")
                  .map((employee) => {
                    const lateCount = attendanceRecords.filter(
                      (record) =>
                        record.employee_id === employee.id &&
                        record.status === "Late" &&
                        new Date(record.date).getMonth() ===
                          new Date().getMonth(),
                    ).length;

                    return (
                      <div
                        key={employee.id}
                        className="flex items-center space-x-2"
                      >
                        <input
                          type="checkbox"
                          id={`penalty-${employee.id}`}
                          checked={selectedEmployeesForPenalty.includes(
                            employee.id,
                          )}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedEmployeesForPenalty([
                                ...selectedEmployeesForPenalty,
                                employee.id,
                              ]);
                            } else {
                              setSelectedEmployeesForPenalty(
                                selectedEmployeesForPenalty.filter(
                                  (id) => id !== employee.id,
                                ),
                              );
                            }
                          }}
                          className="rounded"
                        />
                        <Label
                          htmlFor={`penalty-${employee.id}`}
                          className="text-sm flex-1"
                        >
                          {employee.name} - {employee.position}
                          <span className="text-muted-foreground ml-2">
                            ({lateCount} late days this month)
                          </span>
                        </Label>
                      </div>
                    );
                  })}
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="penalty-reason">Penalty Reason</Label>
              <Textarea
                id="penalty-reason"
                placeholder="Enter reason for penalty..."
                value={penaltyReason}
                onChange={(e) => setPenaltyReason(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="penalty-amount">Penalty Amount ($)</Label>
              <Input
                id="penalty-amount"
                type="number"
                min="0"
                step="0.01"
                placeholder={`Default: $${settings.attendanceRules?.lateArrivalPenalty || 50}`}
                value={penaltyAmount || ""}
                onChange={(e) =>
                  setPenaltyAmount(parseFloat(e.target.value) || 0)
                }
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowLatePenaltyDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={markLatePenalty}
              disabled={
                selectedEmployeesForPenalty.length === 0 || !penaltyReason
              }
              variant="destructive"
            >
              Apply Penalty
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Attendance Record Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Attendance Record</DialogTitle>
            <DialogDescription>
              Modify attendance record for {editingRecord?.employee_name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-checkin-time">Check-in Time</Label>
                <Input
                  id="edit-checkin-time"
                  type="time"
                  value={editCheckInTime}
                  onChange={(e) => setEditCheckInTime(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-checkout-time">Check-out Time</Label>
                <Input
                  id="edit-checkout-time"
                  type="time"
                  value={editCheckOutTime}
                  onChange={(e) => setEditCheckOutTime(e.target.value)}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-notes">Notes</Label>
              <Textarea
                id="edit-notes"
                placeholder="Add any notes about this attendance record..."
                value={editNotes}
                onChange={(e) => setEditNotes(e.target.value)}
              />
            </div>
            {editingRecord && (
              <div className="p-3 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">
                  Employee: {editingRecord.employee_name}
                </p>
                <p className="text-sm text-muted-foreground">
                  Date: {new Date(editingRecord.date).toLocaleDateString()}
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancel
            </Button>
            <Button onClick={saveEditedRecord}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AttendanceDialogs;
