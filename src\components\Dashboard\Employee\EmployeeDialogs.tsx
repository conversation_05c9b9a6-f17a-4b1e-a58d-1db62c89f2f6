import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import EmployeeAttendanceAnalytics from "./EmployeeAttendanceAnalytics";

interface EmployeeDialogsProps {
  showAddEmployeeDialog: boolean;
  setShowAddEmployeeDialog: (show: boolean) => void;
  showEmployeeProfile: boolean;
  setShowEmployeeProfile: (show: boolean) => void;
  selectedEmployee: any;
  setSelectedEmployee: (employee: any) => void;
  newEmployee: any;
  setNewEmployee: (employee: any) => void;
  onAddEmployee: () => void;
  onUpdateEmployee: () => void;
  onAvatarUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onRemoveAvatar: () => void;
}

const EmployeeDialogs = ({
  showAddEmployeeDialog,
  setShowAddEmployeeDialog,
  showEmployeeProfile,
  setShowEmployeeProfile,
  selectedEmployee,
  setSelectedEmployee,
  newEmployee,
  setNewEmployee,
  onAddEmployee,
  onUpdateEmployee,
  onAvatarUpload,
  onRemoveAvatar,
}: EmployeeDialogsProps) => {
  return (
    <>
      {/* Add Employee Dialog */}
      <Dialog
        open={showAddEmployeeDialog}
        onOpenChange={setShowAddEmployeeDialog}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Employee</DialogTitle>
            <DialogDescription>
              Fill in the employee details to add them to the system
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="flex flex-col items-center space-y-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={newEmployee.avatar} alt="Employee avatar" />
                <AvatarFallback>
                  {newEmployee.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("") || "?"}
                </AvatarFallback>
              </Avatar>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    document.getElementById("avatar-upload")?.click()
                  }
                >
                  Upload Photo
                </Button>
                <Button variant="outline" size="sm" onClick={onRemoveAvatar}>
                  Remove
                </Button>
              </div>
              <input
                id="avatar-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={onAvatarUpload}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name *</Label>
                <Input
                  id="name"
                  value={newEmployee.name}
                  onChange={(e) =>
                    setNewEmployee({ ...newEmployee, name: e.target.value })
                  }
                  placeholder="Enter full name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="position">Position *</Label>
                <Input
                  id="position"
                  value={newEmployee.position}
                  onChange={(e) =>
                    setNewEmployee({ ...newEmployee, position: e.target.value })
                  }
                  placeholder="Enter position"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={newEmployee.email}
                  onChange={(e) =>
                    setNewEmployee({ ...newEmployee, email: e.target.value })
                  }
                  placeholder="Enter email address"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={newEmployee.phone}
                  onChange={(e) =>
                    setNewEmployee({ ...newEmployee, phone: e.target.value })
                  }
                  placeholder="Enter phone number"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="salary">Salary *</Label>
                <Input
                  id="salary"
                  type="number"
                  min="0"
                  step="0.01"
                  value={newEmployee.salary || ""}
                  onChange={(e) =>
                    setNewEmployee({
                      ...newEmployee,
                      salary: parseFloat(e.target.value) || 0,
                    })
                  }
                  placeholder="Enter salary"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={newEmployee.status}
                  onValueChange={(
                    value: "Active" | "On Leave" | "Terminated",
                  ) => setNewEmployee({ ...newEmployee, status: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Active">Active</SelectItem>
                    <SelectItem value="On Leave">On Leave</SelectItem>
                    <SelectItem value="Terminated">Terminated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="hire_date">Hire Date</Label>
              <Input
                id="hire_date"
                type="date"
                value={newEmployee.hire_date}
                onChange={(e) =>
                  setNewEmployee({ ...newEmployee, hire_date: e.target.value })
                }
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAddEmployeeDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={onAddEmployee}>Add Employee</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Employee Profile Dialog */}
      <Dialog open={showEmployeeProfile} onOpenChange={setShowEmployeeProfile}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Employee Profile</DialogTitle>
            <DialogDescription>
              View and edit employee information with detailed attendance
              analytics
            </DialogDescription>
          </DialogHeader>
          {selectedEmployee && (
            <div className="grid gap-6 py-4">
              <div className="flex flex-col items-center space-y-4">
                <Avatar className="h-24 w-24">
                  <AvatarImage
                    src={selectedEmployee.avatar}
                    alt={selectedEmployee.name}
                  />
                  <AvatarFallback className="text-lg">
                    {selectedEmployee.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      document.getElementById("profile-avatar-upload")?.click()
                    }
                  >
                    Change Photo
                  </Button>
                  <Button variant="outline" size="sm" onClick={onRemoveAvatar}>
                    Remove
                  </Button>
                </div>
                <input
                  id="profile-avatar-upload"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={onAvatarUpload}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="profile-name">Full Name</Label>
                  <Input
                    id="profile-name"
                    value={selectedEmployee.name}
                    onChange={(e) =>
                      setSelectedEmployee({
                        ...selectedEmployee,
                        name: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="profile-position">Position</Label>
                  <Input
                    id="profile-position"
                    value={selectedEmployee.position}
                    onChange={(e) =>
                      setSelectedEmployee({
                        ...selectedEmployee,
                        position: e.target.value,
                      })
                    }
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="profile-email">Email</Label>
                  <Input
                    id="profile-email"
                    type="email"
                    value={selectedEmployee.email}
                    onChange={(e) =>
                      setSelectedEmployee({
                        ...selectedEmployee,
                        email: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="profile-phone">Phone</Label>
                  <Input
                    id="profile-phone"
                    value={selectedEmployee.phone}
                    onChange={(e) =>
                      setSelectedEmployee({
                        ...selectedEmployee,
                        phone: e.target.value,
                      })
                    }
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="profile-salary">Salary</Label>
                  <Input
                    id="profile-salary"
                    type="number"
                    min="0"
                    step="0.01"
                    value={selectedEmployee.salary}
                    onChange={(e) =>
                      setSelectedEmployee({
                        ...selectedEmployee,
                        salary: parseFloat(e.target.value) || 0,
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="profile-status">Status</Label>
                  <Select
                    value={selectedEmployee.status}
                    onValueChange={(
                      value: "Active" | "On Leave" | "Terminated",
                    ) =>
                      setSelectedEmployee({
                        ...selectedEmployee,
                        status: value,
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="On Leave">On Leave</SelectItem>
                      <SelectItem value="Terminated">Terminated</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="profile-hire-date">Hire Date</Label>
                <Input
                  id="profile-hire-date"
                  type="date"
                  value={selectedEmployee.hire_date}
                  onChange={(e) =>
                    setSelectedEmployee({
                      ...selectedEmployee,
                      hire_date: e.target.value,
                    })
                  }
                />
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Performance Stats</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Attendance Rate:
                        </span>
                        <span className="font-medium">
                          {selectedEmployee.attendance}%
                        </span>
                      </div>
                      <Progress
                        value={selectedEmployee.attendance}
                        className="h-2"
                      />
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Last Check-in:
                        </span>
                        <span className="font-medium text-sm">
                          {selectedEmployee.last_attendance || "N/A"}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Employment Info</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Employee ID:
                        </span>
                        <span className="font-medium">
                          #{selectedEmployee.id}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Hire Date:
                        </span>
                        <span className="font-medium">
                          {new Date(
                            selectedEmployee.hire_date,
                          ).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Status:
                        </span>
                        <Badge
                          variant={
                            selectedEmployee.status === "Active"
                              ? "default"
                              : "secondary"
                          }
                        >
                          {selectedEmployee.status}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Separator />

              {/* Attendance Analytics Section */}
              <EmployeeAttendanceAnalytics employeeId={selectedEmployee.id} />
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowEmployeeProfile(false)}
            >
              Cancel
            </Button>
            <Button onClick={onUpdateEmployee}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EmployeeDialogs;
