// Conditional imports for Electron environment
import { getUserDataPath, isElectron } from "./electron-store";

// Dynamic imports for Node.js modules
let Database: any = null;
let path: any = null;

if (isElectron()) {
  try {
    Database = window.require("better-sqlite3");
    path = window.require("path");
  } catch (error) {
    console.error("Failed to load Node.js modules:", error);
  }
}

const getDatabasePath = () => {
  if (isElectron() && path) {
    const userDataPath = getUserDataPath();
    return path.join(userDataPath, "salon_management.db");
  }
  // Fallback for development
  return "salon_management.db";
};

// Database interfaces
export interface Employee {
  id: number;
  name: string;
  position: string;
  status: "Active" | "On Leave" | "Terminated";
  attendance: number;
  salary: number;
  phone: string;
  email: string;
  hire_date: string;
  last_attendance: string;
  avatar?: string;
  created_at: string;
  updated_at: string;
}

export interface AttendanceRecord {
  id: number;
  employee_id: number;
  employee_name: string;
  date: string;
  check_in: string;
  check_out: string;
  status: "Present" | "Late" | "Absent";
  hours: number;
  late_minutes: number;
  overtime: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface PayrollRecord {
  id: number;
  employee_id: number;
  employee_name: string;
  base_salary: number;
  current_salary: number;
  month: string;
  penalties: number;
  raises: number;
  advances: number;
  overtime_bonus: number;
  status: "Paid" | "Pending";
  last_reset: string;
  created_at: string;
  updated_at: string;
}

export interface Penalty {
  id: number;
  employee_id: number;
  employee_name: string;
  reason: string;
  amount: number;
  date: string;
  type: string;
  created_at: string;
}

export interface Advance {
  id: number;
  employee_id: number;
  employee_name: string;
  amount: number;
  reason: string;
  date: string;
  month: string;
  status: "active" | "paid";
  created_at: string;
}

export interface Holiday {
  id: number;
  name: string;
  date: string;
  created_at: string;
}

export interface Settings {
  id: number;
  key: string;
  value: string;
  updated_at: string;
}

export interface Customer {
  id: number;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  notes?: string;
  instagram?: string;
  facebook?: string;
  twitter?: string;
  total_bookings: number;
  total_spent: number;
  last_visit?: string;
  favorite_services?: string; // JSON array of service names
  created_at: string;
  updated_at: string;
}

export interface Booking {
  id: number;
  title: string;
  notes: string;
  date: string;
  customer_id: number | null;
  customer_name: string;
  employee_ids: string; // JSON array of employee IDs
  start_time: string;
  end_time: string;
  service_ids: string; // JSON array of service IDs
  package_ids: string; // JSON array of package IDs
  dress_ids: string; // JSON array of dress IDs
  dress_rental_start_date?: string; // Start date for dress rental period
  dress_rental_end_date?: string; // End date for dress rental period
  total_amount: number;
  deposit: number;
  discount_percentage: number;
  status: "Confirmed" | "In Progress" | "Cancelled" | "Completed";
  created_at: string;
  updated_at: string;
}

export interface Service {
  id: number;
  name: string;
  duration: number; // in minutes
  price: number;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface Package {
  id: number;
  name: string;
  price: number;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface PackageService {
  id: number;
  package_id: number;
  service_id: number;
  created_at: string;
}

export interface Dress {
  id: number;
  name: string;
  rental_price: number;
  status: "Available" | "Rented" | "Maintenance";
  colors: string; // JSON array of colors
  photos: string; // JSON array of photo URLs
  description?: string;
  created_at: string;
  updated_at: string;
}

class DatabaseManager {
  private static instance: DatabaseManager;
  private db: Database.Database;

  private constructor() {
    if (!isElectron() || !Database) {
      // Use mock data for browser environment
      console.log("Running in browser mode - using mock data");
      this.initializeMockData();
      return;
    }

    // Initialize database with real SQLite for Electron
    const dbPath = getDatabasePath();
    console.log("Initializing SQLite database at:", dbPath);
    this.db = new Database(dbPath);
    this.initializeDatabase();
    this.setupTriggers();
  }

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  private mockData = {
    employees: [] as Employee[],
    attendanceRecords: [] as AttendanceRecord[],
    payrollRecords: [] as PayrollRecord[],
    penalties: [] as Penalty[],
    advances: [] as Advance[],
    holidays: [] as Holiday[],
    settings: {} as any,
    services: [] as Service[],
    packages: [] as Package[],
    packageServices: [] as PackageService[],
    dresses: [] as Dress[],
    customers: [] as Customer[],
    bookings: [] as Booking[],
  };

  private initializeMockData() {
    // Initialize with default mock data
    this.mockData.employees = [
      {
        id: 1,
        name: "Emma Johnson",
        position: "Senior Stylist",
        status: "Active" as const,
        attendance: 95,
        salary: 2500,
        phone: "+****************",
        email: "<EMAIL>",
        hire_date: "2022-01-15",
        last_attendance: "2024-01-15 08:30 AM",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Emma",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 2,
        name: "David Smith",
        position: "Beautician",
        status: "Active" as const,
        attendance: 88,
        salary: 2200,
        phone: "+****************",
        email: "<EMAIL>",
        hire_date: "2022-03-20",
        last_attendance: "2024-01-15 08:45 AM",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=David",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];

    // Initialize mock customers
    this.mockData.customers = [
      {
        id: 1,
        name: "Jane Smith",
        phone: "+****************",
        email: "<EMAIL>",
        address: "123 Main St, City, State 12345",
        notes: "Prefers natural hair colors, allergic to certain chemicals",
        instagram: "@janesmith",
        facebook: "jane.smith",
        twitter: "@jane_smith",
        total_bookings: 12,
        total_spent: 1450.0,
        last_visit: "2024-01-10",
        favorite_services: JSON.stringify(["Hair Styling", "Hair Coloring"]),
        created_at: "2023-06-15T10:30:00Z",
        updated_at: new Date().toISOString(),
      },
      {
        id: 2,
        name: "Robert Johnson",
        phone: "+****************",
        email: "<EMAIL>",
        address: "456 Oak Ave, City, State 12345",
        notes: "Regular customer, books monthly appointments",
        instagram: "",
        facebook: "robert.johnson",
        twitter: "",
        total_bookings: 8,
        total_spent: 680.0,
        last_visit: "2024-01-08",
        favorite_services: JSON.stringify(["Hair Cut", "Beard Trim"]),
        created_at: "2023-08-20T14:15:00Z",
        updated_at: new Date().toISOString(),
      },
    ];

    // Initialize mock bookings
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dayAfterTomorrow = new Date(today);
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 3);

    this.mockData.bookings = [
      {
        id: 1,
        title: "Hair Styling Session",
        notes: "Regular customer, prefers natural look",
        date: new Date().toISOString().split("T")[0],
        customer_id: 1,
        customer_name: "Jane Smith",
        employee_ids: JSON.stringify([1]),
        start_time: "09:00",
        end_time: "10:00",
        service_ids: JSON.stringify([1]),
        package_ids: JSON.stringify([]),
        dress_ids: JSON.stringify([]),
        total_amount: 35,
        deposit: 0,
        discount_percentage: 0,
        status: "Confirmed" as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 2,
        title: "Complete Makeover Package",
        notes: "Special occasion - wedding guest",
        date: new Date().toISOString().split("T")[0],
        customer_id: 2,
        customer_name: "Robert Johnson",
        employee_ids: JSON.stringify([1, 2]),
        start_time: "14:00",
        end_time: "17:00",
        service_ids: JSON.stringify([]),
        package_ids: JSON.stringify([1]),
        dress_ids: JSON.stringify([1]),
        dress_rental_start_date: tomorrow.toISOString().split("T")[0],
        dress_rental_end_date: dayAfterTomorrow.toISOString().split("T")[0],
        total_amount: 330,
        deposit: 50,
        discount_percentage: 10,
        status: "Confirmed" as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];

    // Update dress status based on bookings
    this.updateDressStatusFromBookings();

    this.mockData.settings = {
      workingDays: {
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: false,
        sunday: false,
      },
      workingHours: {
        startTime: "09:00",
        endTime: "17:00",
      },
      attendanceRules: {
        lateArrivalPenalty: 50,
        absencePenalty: 100,
        allowanceTime: 15,
        overtimeReward: 25,
      },
      layoutSettings: {
        darkMode: false,
        rtlMode: false,
      },
      holidays: [],
    };

    // Initialize mock services
    this.mockData.services = [
      {
        id: 1,
        name: "Hair Cut",
        duration: 30,
        price: 35,
        description: "Professional hair cutting service",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 2,
        name: "Hair Coloring",
        duration: 120,
        price: 120,
        description: "Full hair coloring service",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 3,
        name: "Manicure",
        duration: 45,
        price: 40,
        description: "Professional nail care",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];

    // Initialize mock packages
    this.mockData.packages = [
      {
        id: 1,
        name: "Complete Makeover",
        price: 180,
        description: "Hair cut, coloring, and styling package",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];

    // Initialize mock package services
    this.mockData.packageServices = [
      {
        id: 1,
        package_id: 1,
        service_id: 1,
        created_at: new Date().toISOString(),
      },
      {
        id: 2,
        package_id: 1,
        service_id: 2,
        created_at: new Date().toISOString(),
      },
    ];

    // Initialize mock dresses
    this.mockData.dresses = [
      {
        id: 1,
        name: "Evening Gown - Elegant Black",
        rental_price: 150,
        status: "Available" as const,
        colors: JSON.stringify(["Black", "Navy"]),
        photos: JSON.stringify([
          "https://images.unsplash.com/photo-1566479179817-c0b5b4b8b1b1?w=400&q=80",
        ]),
        description: "Elegant evening gown perfect for special occasions",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 2,
        name: "Wedding Dress - Classic White",
        rental_price: 300,
        status: "Rented" as const,
        colors: JSON.stringify(["White", "Ivory"]),
        photos: JSON.stringify([
          "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&q=80",
        ]),
        description: "Beautiful classic wedding dress",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];
  }

  private initializeDatabase() {
    // Enable foreign keys
    this.db.pragma("foreign_keys = ON");

    // Create tables
    this.createTables();
    this.insertDefaultData();
  }

  private createTables() {
    // Employees table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS employees (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        position TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'Active',
        attendance REAL DEFAULT 0,
        salary REAL NOT NULL,
        phone TEXT,
        email TEXT UNIQUE,
        hire_date TEXT NOT NULL,
        last_attendance TEXT,
        avatar TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Attendance records table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS attendance_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        employee_name TEXT NOT NULL,
        date TEXT NOT NULL,
        check_in TEXT NOT NULL,
        check_out TEXT DEFAULT '',
        status TEXT NOT NULL DEFAULT 'Present',
        hours REAL DEFAULT 0,
        late_minutes INTEGER DEFAULT 0,
        overtime REAL DEFAULT 0,
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE,
        UNIQUE(employee_id, date)
      )
    `);

    // Payroll records table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS payroll_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        employee_name TEXT NOT NULL,
        base_salary REAL NOT NULL,
        current_salary REAL NOT NULL,
        month TEXT NOT NULL,
        penalties REAL DEFAULT 0,
        raises REAL DEFAULT 0,
        advances REAL DEFAULT 0,
        overtime_bonus REAL DEFAULT 0,
        status TEXT DEFAULT 'Pending',
        last_reset TEXT DEFAULT CURRENT_TIMESTAMP,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE,
        UNIQUE(employee_id, month)
      )
    `);

    // Penalties table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS penalties (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        employee_name TEXT NOT NULL,
        reason TEXT NOT NULL,
        amount REAL NOT NULL,
        date TEXT NOT NULL,
        type TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE
      )
    `);

    // Advances table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS advances (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        employee_name TEXT NOT NULL,
        amount REAL NOT NULL,
        reason TEXT NOT NULL,
        date TEXT NOT NULL,
        month TEXT NOT NULL,
        status TEXT DEFAULT 'active',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE
      )
    `);

    // Holidays table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS holidays (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        date TEXT NOT NULL UNIQUE,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Settings table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT NOT NULL UNIQUE,
        value TEXT NOT NULL,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Services table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS services (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        duration INTEGER NOT NULL,
        price REAL NOT NULL,
        description TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Packages table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS packages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        price REAL NOT NULL,
        description TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Package services junction table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS package_services (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        package_id INTEGER NOT NULL,
        service_id INTEGER NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (package_id) REFERENCES packages (id) ON DELETE CASCADE,
        FOREIGN KEY (service_id) REFERENCES services (id) ON DELETE CASCADE,
        UNIQUE(package_id, service_id)
      )
    `);

    // Dresses table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS dresses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        rental_price REAL NOT NULL,
        status TEXT NOT NULL DEFAULT 'Available',
        colors TEXT NOT NULL DEFAULT '[]',
        photos TEXT NOT NULL DEFAULT '[]',
        description TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Monthly reset log table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS monthly_resets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        month TEXT NOT NULL UNIQUE,
        reset_date TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Customers table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        notes TEXT,
        instagram TEXT,
        facebook TEXT,
        twitter TEXT,
        total_bookings INTEGER DEFAULT 0,
        total_spent REAL DEFAULT 0,
        last_visit TEXT,
        favorite_services TEXT DEFAULT '[]',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Bookings table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS bookings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        notes TEXT DEFAULT '',
        date TEXT NOT NULL,
        customer_id INTEGER,
        customer_name TEXT NOT NULL,
        employee_ids TEXT DEFAULT '[]',
        start_time TEXT DEFAULT '',
        end_time TEXT DEFAULT '',
        service_ids TEXT DEFAULT '[]',
        package_ids TEXT DEFAULT '[]',
        dress_ids TEXT DEFAULT '[]',
        dress_rental_start_date TEXT,
        dress_rental_end_date TEXT,
        total_amount REAL NOT NULL DEFAULT 0,
        deposit REAL DEFAULT 0,
        discount_percentage REAL DEFAULT 0,
        status TEXT NOT NULL DEFAULT 'Confirmed',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE SET NULL
      )
    `);

    // Add columns to existing bookings table if they don't exist
    this.db.exec(`
      ALTER TABLE bookings ADD COLUMN dress_rental_start_date TEXT;
    `);
    this.db.exec(`
      ALTER TABLE bookings ADD COLUMN dress_rental_end_date TEXT;
    `);
  }

  private setupTriggers() {
    // Update timestamp triggers
    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_employees_timestamp 
      AFTER UPDATE ON employees
      BEGIN
        UPDATE employees SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);

    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_attendance_timestamp 
      AFTER UPDATE ON attendance_records
      BEGIN
        UPDATE attendance_records SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);

    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_payroll_timestamp 
      AFTER UPDATE ON payroll_records
      BEGIN
        UPDATE payroll_records SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);

    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_settings_timestamp 
      AFTER UPDATE ON settings
      BEGIN
        UPDATE settings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);

    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_services_timestamp 
      AFTER UPDATE ON services
      BEGIN
        UPDATE services SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);

    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_packages_timestamp 
      AFTER UPDATE ON packages
      BEGIN
        UPDATE packages SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);

    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_dresses_timestamp 
      AFTER UPDATE ON dresses
      BEGIN
        UPDATE dresses SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);

    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_customers_timestamp 
      AFTER UPDATE ON customers
      BEGIN
        UPDATE customers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);

    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_bookings_timestamp 
      AFTER UPDATE ON bookings
      BEGIN
        UPDATE bookings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);
  }

  private insertDefaultData() {
    // Check if data already exists
    const employeeCount = this.db
      .prepare("SELECT COUNT(*) as count FROM employees")
      .get() as { count: number } | null;

    if (!employeeCount || employeeCount.count === 0) {
      // Insert default employees
      const insertEmployee = this.db.prepare(`
        INSERT INTO employees (name, position, status, attendance, salary, phone, email, hire_date, last_attendance, avatar)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const defaultEmployees = [
        [
          "Emma Johnson",
          "Senior Stylist",
          "Active",
          95,
          2500,
          "+****************",
          "<EMAIL>",
          "2022-01-15",
          "2024-01-15 08:30 AM",
          "https://api.dicebear.com/7.x/avataaars/svg?seed=Emma",
        ],
        [
          "David Smith",
          "Beautician",
          "Active",
          88,
          2200,
          "+****************",
          "<EMAIL>",
          "2022-03-20",
          "2024-01-15 08:45 AM",
          "https://api.dicebear.com/7.x/avataaars/svg?seed=David",
        ],
        [
          "Lisa Brown",
          "Receptionist",
          "Active",
          100,
          1800,
          "+****************",
          "<EMAIL>",
          "2021-11-10",
          "2024-01-15 08:30 AM",
          "https://api.dicebear.com/7.x/avataaars/svg?seed=Lisa",
        ],
        [
          "Michael Davis",
          "Manager",
          "Active",
          92,
          3500,
          "+1 (555) 456-7890",
          "<EMAIL>",
          "2020-08-05",
          "2024-01-15 08:15 AM",
          "https://api.dicebear.com/7.x/avataaars/svg?seed=Michael",
        ],
        [
          "Sarah Wilson",
          "Makeup Artist",
          "On Leave",
          75,
          2000,
          "+1 (555) 567-8901",
          "<EMAIL>",
          "2023-02-28",
          "2024-01-10 08:30 AM",
          "https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah",
        ],
        [
          "Anna Martinez",
          "Nail Technician",
          "Active",
          96,
          1900,
          "+1 (555) 678-9012",
          "<EMAIL>",
          "2023-05-12",
          "2024-01-15 08:25 AM",
          "https://api.dicebear.com/7.x/avataaars/svg?seed=Anna",
        ],
      ];

      const transaction = this.db.transaction(() => {
        for (const employee of defaultEmployees) {
          insertEmployee.run(...employee);
        }
      });
      transaction();

      // Insert default attendance records
      this.insertDefaultAttendance();

      // Insert default holidays
      this.insertDefaultHolidays();

      // Insert default settings
      this.insertDefaultSettings();

      // Insert default services, packages, dresses, customers, and bookings
      this.insertDefaultServices();
      this.insertDefaultPackages();
      this.insertDefaultDresses();
      this.insertDefaultCustomers();
      this.insertDefaultBookings();

      // Initialize payroll records
      this.initializePayrollRecords();
    }
  }

  private insertDefaultAttendance() {
    const insertAttendance = this.db.prepare(`
      INSERT INTO attendance_records (employee_id, employee_name, date, check_in, check_out, status, hours, late_minutes, overtime)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const defaultRecords = [
      [
        1,
        "Emma Johnson",
        "2024-01-15",
        "08:30 AM",
        "05:30 PM",
        "Present",
        9,
        0,
        0,
      ],
      [
        2,
        "David Smith",
        "2024-01-15",
        "08:45 AM",
        "05:15 PM",
        "Late",
        8.5,
        15,
        0,
      ],
      [
        3,
        "Lisa Brown",
        "2024-01-15",
        "08:30 AM",
        "05:30 PM",
        "Present",
        9,
        0,
        0,
      ],
      [
        4,
        "Michael Davis",
        "2024-01-15",
        "08:15 AM",
        "06:00 PM",
        "Present",
        9.75,
        0,
        0.75,
      ],
      [
        6,
        "Anna Martinez",
        "2024-01-15",
        "08:25 AM",
        "05:25 PM",
        "Present",
        9,
        0,
        0,
      ],
    ];

    const transaction = this.db.transaction(() => {
      for (const record of defaultRecords) {
        insertAttendance.run(...record);
      }
    });
    transaction();
  }

  private insertDefaultServices() {
    const insertService = this.db.prepare(`
      INSERT INTO services (name, duration, price, description)
      VALUES (?, ?, ?, ?)
    `);

    const defaultServices = [
      ["Hair Cut", 30, 35, "Professional hair cutting service"],
      ["Hair Coloring", 120, 120, "Full hair coloring service"],
      ["Manicure", 45, 40, "Professional nail care"],
      ["Facial", 60, 80, "Relaxing facial treatment"],
      ["Makeup", 90, 100, "Professional makeup application"],
    ];

    const transaction = this.db.transaction(() => {
      for (const service of defaultServices) {
        insertService.run(...service);
      }
    });
    transaction();
  }

  private insertDefaultPackages() {
    const insertPackage = this.db.prepare(`
      INSERT INTO packages (name, price, description)
      VALUES (?, ?, ?)
    `);

    const defaultPackages = [
      ["Complete Makeover", 180, "Hair cut, coloring, and styling package"],
      ["Bridal Package", 250, "Complete bridal beauty package"],
    ];

    const transaction = this.db.transaction(() => {
      for (const pkg of defaultPackages) {
        insertPackage.run(...pkg);
      }
    });
    transaction();

    // Add services to packages
    const insertPackageService = this.db.prepare(`
      INSERT INTO package_services (package_id, service_id)
      VALUES (?, ?)
    `);

    const packageServiceRelations = [
      [1, 1], // Complete Makeover includes Hair Cut
      [1, 2], // Complete Makeover includes Hair Coloring
      [2, 1], // Bridal Package includes Hair Cut
      [2, 2], // Bridal Package includes Hair Coloring
      [2, 5], // Bridal Package includes Makeup
    ];

    const relationTransaction = this.db.transaction(() => {
      for (const relation of packageServiceRelations) {
        insertPackageService.run(...relation);
      }
    });
    relationTransaction();
  }

  private insertDefaultDresses() {
    const insertDress = this.db.prepare(`
      INSERT INTO dresses (name, rental_price, status, colors, photos, description)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    const defaultDresses = [
      [
        "Evening Gown - Elegant Black",
        150,
        "Available",
        JSON.stringify(["Black", "Navy"]),
        JSON.stringify([
          "https://images.unsplash.com/photo-1566479179817-c0b5b4b8b1b1?w=400&q=80",
        ]),
        "Elegant evening gown perfect for special occasions",
      ],
      [
        "Wedding Dress - Classic White",
        300,
        "Available",
        JSON.stringify(["White", "Ivory"]),
        JSON.stringify([
          "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&q=80",
        ]),
        "Beautiful classic wedding dress",
      ],
      [
        "Cocktail Dress - Red",
        100,
        "Rented",
        JSON.stringify(["Red", "Burgundy"]),
        JSON.stringify([
          "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&q=80",
        ]),
        "Stylish cocktail dress for parties",
      ],
    ];

    const transaction = this.db.transaction(() => {
      for (const dress of defaultDresses) {
        insertDress.run(...dress);
      }
    });
    transaction();
  }

  private insertDefaultCustomers() {
    const insertCustomer = this.db.prepare(`
      INSERT INTO customers (name, phone, email, address, notes, instagram, facebook, twitter, total_bookings, total_spent, last_visit, favorite_services)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const defaultCustomers = [
      [
        "Jane Smith",
        "+****************",
        "<EMAIL>",
        "123 Main St, City, State 12345",
        "Prefers natural hair colors, allergic to certain chemicals",
        "@janesmith",
        "jane.smith",
        "@jane_smith",
        12,
        1450.0,
        "2024-01-10",
        JSON.stringify(["Hair Styling", "Hair Coloring"]),
      ],
      [
        "Robert Johnson",
        "+****************",
        "<EMAIL>",
        "456 Oak Ave, City, State 12345",
        "Regular customer, books monthly appointments",
        "",
        "robert.johnson",
        "",
        8,
        680.0,
        "2024-01-08",
        JSON.stringify(["Hair Cut", "Beard Trim"]),
      ],
      [
        "Emily Davis",
        "+****************",
        "<EMAIL>",
        "789 Pine St, City, State 12345",
        "Special occasion client, books for events",
        "@emily_davis",
        "",
        "@emilyd",
        15,
        2100.0,
        "2024-01-12",
        JSON.stringify(["Complete Makeover", "Manicure"]),
      ],
    ];

    const transaction = this.db.transaction(() => {
      for (const customer of defaultCustomers) {
        insertCustomer.run(...customer);
      }
    });
    transaction();
  }

  private insertDefaultBookings() {
    const insertBooking = this.db.prepare(`
      INSERT INTO bookings (title, notes, date, customer_id, customer_name, employee_ids, start_time, end_time, service_ids, package_ids, dress_ids, dress_rental_start_date, dress_rental_end_date, total_amount, deposit, discount_percentage, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const today = new Date().toISOString().split("T")[0];
    const tomorrow = new Date(new Date(today).getTime() + 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0];
    const dayAfterTomorrow = new Date(
      new Date(today).getTime() + 3 * 24 * 60 * 60 * 1000,
    )
      .toISOString()
      .split("T")[0];

    const defaultBookings = [
      [
        "Hair Styling Session",
        "Regular customer, prefers natural look",
        today,
        1,
        "Jane Smith",
        JSON.stringify([1]),
        "09:00",
        "10:00",
        JSON.stringify([1]),
        JSON.stringify([]),
        JSON.stringify([]),
        null, // dress_rental_start_date
        null, // dress_rental_end_date
        35,
        0,
        0,
        "Confirmed",
      ],
      [
        "Complete Makeover Package",
        "Special occasion - wedding guest",
        today,
        2,
        "Robert Johnson",
        JSON.stringify([1, 2]),
        "14:00",
        "17:00",
        JSON.stringify([]),
        JSON.stringify([1]),
        JSON.stringify([1]),
        tomorrow, // dress_rental_start_date
        dayAfterTomorrow, // dress_rental_end_date
        330,
        50,
        10,
        "Confirmed",
      ],
    ];

    const transaction = this.db.transaction(() => {
      for (const booking of defaultBookings) {
        insertBooking.run(...booking);
      }
    });
    transaction();

    // Update dress status based on bookings
    this.updateDressStatusFromBookings();
  }

  private insertDefaultHolidays() {
    const insertHoliday = this.db.prepare(`
      INSERT INTO holidays (name, date) VALUES (?, ?)
    `);

    const defaultHolidays = [
      ["New Year's Day", "2024-01-01"],
      ["Independence Day", "2024-07-04"],
      ["Christmas Day", "2024-12-25"],
    ];

    const transaction = this.db.transaction(() => {
      for (const holiday of defaultHolidays) {
        insertHoliday.run(...holiday);
      }
    });
    transaction();
  }

  private insertDefaultSettings() {
    const insertSetting = this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)
    `);

    const defaultSettings = {
      working_days: JSON.stringify({
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: false,
        sunday: false,
      }),
      working_hours: JSON.stringify({
        startTime: "09:00",
        endTime: "17:00",
      }),
      attendance_rules: JSON.stringify({
        lateArrivalPenalty: 50,
        absencePenalty: 100,
        allowanceTime: 15,
        overtimeReward: 25,
      }),
      layout_settings: JSON.stringify({
        darkMode: false,
        rtlMode: false,
      }),
    };

    const transaction = this.db.transaction(() => {
      if (defaultSettings.working_days) {
        insertSetting.run("working_days", defaultSettings.working_days);
      }
      if (defaultSettings.working_hours) {
        insertSetting.run("working_hours", defaultSettings.working_hours);
      }
      if (defaultSettings.attendance_rules) {
        insertSetting.run("attendance_rules", defaultSettings.attendance_rules);
      }
      if (defaultSettings.layout_settings) {
        insertSetting.run("layout_settings", defaultSettings.layout_settings);
      }
    });
    transaction();
  }

  private initializePayrollRecords() {
    const employees = this.getEmployees();
    const currentMonth = new Date().toISOString().slice(0, 7);

    const insertPayroll = this.db.prepare(`
      INSERT OR REPLACE INTO payroll_records 
      (employee_id, employee_name, base_salary, current_salary, month, status)
      VALUES (?, ?, ?, ?, ?, 'Pending')
    `);

    const transaction = this.db.transaction(() => {
      for (const employee of employees) {
        insertPayroll.run(
          employee.id,
          employee.name,
          employee.salary,
          employee.salary,
          currentMonth,
        );
      }
    });
    transaction();
  }

  // Employee operations
  getEmployees(): Employee[] {
    if (!this.db) {
      return this.mockData.employees;
    }
    return this.db
      .prepare("SELECT * FROM employees ORDER BY name")
      .all() as Employee[];
  }

  getEmployeeById(id: number): Employee | null {
    if (!this.db) {
      return this.mockData.employees.find((emp) => emp.id === id) || null;
    }
    return (
      (this.db
        .prepare("SELECT * FROM employees WHERE id = ?")
        .get(id) as Employee) || null
    );
  }

  addEmployee(
    employee: Omit<Employee, "id" | "created_at" | "updated_at">,
  ): Employee {
    if (!this.db) {
      const newEmployee: Employee = {
        ...employee,
        id: Math.max(0, ...this.mockData.employees.map((e) => e.id)) + 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      this.mockData.employees.push(newEmployee);
      return newEmployee;
    }

    const insert = this.db.prepare(`
      INSERT INTO employees (name, position, status, attendance, salary, phone, email, hire_date, last_attendance, avatar)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = insert.run(
      employee.name,
      employee.position,
      employee.status,
      employee.attendance,
      employee.salary,
      employee.phone,
      employee.email,
      employee.hire_date,
      employee.last_attendance,
      employee.avatar || null,
    );

    return this.getEmployeeById(result.lastInsertRowid as number)!;
  }

  updateEmployee(id: number, updates: Partial<Employee>): Employee | null {
    if (!this.db) {
      const employeeIndex = this.mockData.employees.findIndex(
        (emp) => emp.id === id,
      );
      if (employeeIndex === -1) return null;

      this.mockData.employees[employeeIndex] = {
        ...this.mockData.employees[employeeIndex],
        ...updates,
        updated_at: new Date().toISOString(),
      };
      return this.mockData.employees[employeeIndex];
    }

    const fields = Object.keys(updates)
      .filter((key) => key !== "id")
      .map((key) => `${key} = ?`)
      .join(", ");
    const values = Object.entries(updates)
      .filter(([key]) => key !== "id")
      .map(([, value]) => value);

    if (fields.length === 0) return this.getEmployeeById(id);

    const update = this.db.prepare(
      `UPDATE employees SET ${fields} WHERE id = ?`,
    );
    update.run(...values, id);

    return this.getEmployeeById(id);
  }

  // Attendance operations
  getAttendanceRecords(): AttendanceRecord[] {
    if (!this.db) {
      return this.mockData.attendanceRecords;
    }
    return this.db
      .prepare(
        "SELECT * FROM attendance_records ORDER BY date DESC, employee_name",
      )
      .all() as AttendanceRecord[];
  }

  getAttendanceByEmployee(employeeId: number): AttendanceRecord[] {
    if (!this.db) {
      return this.mockData.attendanceRecords.filter(
        (record) => record.employee_id === employeeId,
      );
    }
    return this.db
      .prepare(
        "SELECT * FROM attendance_records WHERE employee_id = ? ORDER BY date DESC",
      )
      .all(employeeId) as AttendanceRecord[];
  }

  getAttendanceByDate(date: string): AttendanceRecord[] {
    if (!this.db) {
      return this.mockData.attendanceRecords.filter(
        (record) => record.date === date,
      );
    }
    return this.db
      .prepare(
        "SELECT * FROM attendance_records WHERE date = ? ORDER BY employee_name",
      )
      .all(date) as AttendanceRecord[];
  }

  addAttendanceRecord(
    record: Omit<AttendanceRecord, "id" | "created_at" | "updated_at">,
  ): AttendanceRecord {
    if (!this.db) {
      const newRecord: AttendanceRecord = {
        ...record,
        id:
          Math.max(0, ...this.mockData.attendanceRecords.map((r) => r.id)) + 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      this.mockData.attendanceRecords.push(newRecord);

      // Apply penalty for absent employees
      if (record.status === "Absent") {
        this.applyAbsentPenalty(record.employee_id, record.employee_name);
      }

      return newRecord;
    }

    const insert = this.db.prepare(`
      INSERT OR REPLACE INTO attendance_records 
      (employee_id, employee_name, date, check_in, check_out, status, hours, late_minutes, overtime, notes)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = insert.run(
      record.employee_id,
      record.employee_name,
      record.date,
      record.check_in,
      record.check_out,
      record.status,
      record.hours,
      record.late_minutes,
      record.overtime,
      record.notes || "",
    );

    // Apply penalty for absent employees
    if (record.status === "Absent") {
      this.applyAbsentPenalty(record.employee_id, record.employee_name);
    }

    return this.db
      .prepare(
        "SELECT * FROM attendance_records WHERE employee_id = ? AND date = ?",
      )
      .get(record.employee_id, record.date) as AttendanceRecord;
  }

  updateAttendanceRecord(
    id: number,
    updates: Partial<AttendanceRecord>,
  ): AttendanceRecord | null {
    if (!this.db) {
      const recordIndex = this.mockData.attendanceRecords.findIndex(
        (r) => r.id === id,
      );
      if (recordIndex === -1) return null;

      const oldRecord = this.mockData.attendanceRecords[recordIndex];
      this.mockData.attendanceRecords[recordIndex] = {
        ...oldRecord,
        ...updates,
        updated_at: new Date().toISOString(),
      };

      // Apply penalty if status changed to Absent
      if (updates.status === "Absent" && oldRecord.status !== "Absent") {
        this.applyAbsentPenalty(oldRecord.employee_id, oldRecord.employee_name);
      }

      return this.mockData.attendanceRecords[recordIndex];
    }

    // Get the old record first
    const oldRecord = this.db
      .prepare("SELECT * FROM attendance_records WHERE id = ?")
      .get(id) as AttendanceRecord;

    const fields = Object.keys(updates)
      .filter((key) => !["id", "created_at", "updated_at"].includes(key))
      .map((key) => `${key} = ?`)
      .join(", ");
    const values = Object.entries(updates)
      .filter(([key]) => !["id", "created_at", "updated_at"].includes(key))
      .map(([, value]) => value);

    if (fields.length === 0) return oldRecord;

    const update = this.db.prepare(
      `UPDATE attendance_records SET ${fields} WHERE id = ?`,
    );
    update.run(...values, id);

    // Apply penalty if status changed to Absent
    if (
      updates.status === "Absent" &&
      oldRecord &&
      oldRecord.status !== "Absent"
    ) {
      this.applyAbsentPenalty(oldRecord.employee_id, oldRecord.employee_name);
    }

    return this.db
      .prepare("SELECT * FROM attendance_records WHERE id = ?")
      .get(id) as AttendanceRecord;
  }

  resetEmployeeOvertime(employeeId: number, month?: string): boolean {
    if (!this.db) {
      const targetMonth = month || new Date().toISOString().slice(0, 7);
      const recordsToUpdate = this.mockData.attendanceRecords.filter(
        (r) => r.employee_id === employeeId && r.date.startsWith(targetMonth),
      );
      recordsToUpdate.forEach((record) => {
        record.overtime = 0;
        record.updated_at = new Date().toISOString();
      });
      return recordsToUpdate.length > 0;
    }

    const targetMonth = month || new Date().toISOString().slice(0, 7);
    const result = this.db
      .prepare(
        `UPDATE attendance_records SET overtime = 0, updated_at = CURRENT_TIMESTAMP 
         WHERE employee_id = ? AND date LIKE ?`,
      )
      .run(employeeId, `${targetMonth}%`);

    return result.changes > 0;
  }

  // Payroll operations
  getPayrollRecords(): PayrollRecord[] {
    if (!this.db) {
      return this.mockData.payrollRecords;
    }
    const currentMonth = new Date().toISOString().slice(0, 7);
    return this.db
      .prepare(
        "SELECT * FROM payroll_records WHERE month = ? ORDER BY employee_name",
      )
      .all(currentMonth) as PayrollRecord[];
  }

  getPayrollByEmployee(
    employeeId: number,
    month?: string,
  ): PayrollRecord | null {
    if (!this.db) {
      const targetMonth = month || new Date().toISOString().slice(0, 7);
      return (
        this.mockData.payrollRecords.find(
          (record) =>
            record.employee_id === employeeId && record.month === targetMonth,
        ) || null
      );
    }
    const targetMonth = month || new Date().toISOString().slice(0, 7);
    return (
      (this.db
        .prepare(
          "SELECT * FROM payroll_records WHERE employee_id = ? AND month = ?",
        )
        .get(employeeId, targetMonth) as PayrollRecord) || null
    );
  }

  updatePayrollRecord(
    employeeId: number,
    updates: Partial<PayrollRecord>,
  ): PayrollRecord | null {
    if (!this.db) {
      const currentMonth = new Date().toISOString().slice(0, 7);
      const recordIndex = this.mockData.payrollRecords.findIndex(
        (record) =>
          record.employee_id === employeeId && record.month === currentMonth,
      );
      if (recordIndex === -1) return null;

      this.mockData.payrollRecords[recordIndex] = {
        ...this.mockData.payrollRecords[recordIndex],
        ...updates,
        updated_at: new Date().toISOString(),
      };
      return this.mockData.payrollRecords[recordIndex];
    }

    const currentMonth = new Date().toISOString().slice(0, 7);
    const fields = Object.keys(updates)
      .filter(
        (key) =>
          !["id", "employee_id", "month", "created_at", "updated_at"].includes(
            key,
          ),
      )
      .map((key) => `${key} = ?`)
      .join(", ");
    const values = Object.entries(updates)
      .filter(
        ([key]) =>
          !["id", "employee_id", "month", "created_at", "updated_at"].includes(
            key,
          ),
      )
      .map(([, value]) => value);

    if (fields.length === 0)
      return this.getPayrollByEmployee(employeeId, currentMonth);

    const update = this.db.prepare(
      `UPDATE payroll_records SET ${fields} WHERE employee_id = ? AND month = ?`,
    );
    update.run(...values, employeeId, currentMonth);

    return this.getPayrollByEmployee(employeeId, currentMonth);
  }

  resetMonthlyPayroll(): void {
    if (!this.db) {
      // Mock implementation for browser mode
      const currentMonth = new Date().toISOString().slice(0, 7);
      const activeEmployees = this.mockData.employees.filter(
        (emp) => emp.status === "Active",
      );

      // Reset payroll records in mock data
      this.mockData.payrollRecords = activeEmployees.map((employee) => ({
        id: Math.max(0, ...this.mockData.payrollRecords.map((r) => r.id)) + 1,
        employee_id: employee.id,
        employee_name: employee.name,
        base_salary: employee.salary,
        current_salary: employee.salary,
        month: currentMonth,
        penalties: 0,
        raises: 0,
        advances: 0,
        overtime_bonus: 0,
        status: "Pending" as const,
        last_reset: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));

      // Mark advances as paid
      this.mockData.advances.forEach((advance) => {
        if (advance.status === "active") {
          advance.status = "paid";
        }
      });
      return;
    }

    const currentMonth = new Date().toISOString().slice(0, 7);

    // Check if already reset this month
    const existingReset = this.db
      .prepare("SELECT * FROM monthly_resets WHERE month = ?")
      .get(currentMonth);
    if (existingReset) return;

    const transaction = this.db.transaction(() => {
      // Get all active employees
      const employees = this.db
        .prepare('SELECT * FROM employees WHERE status = "Active"')
        .all() as Employee[];

      // Reset payroll records
      const upsertPayroll = this.db.prepare(`
        INSERT OR REPLACE INTO payroll_records 
        (employee_id, employee_name, base_salary, current_salary, month, penalties, raises, advances, overtime_bonus, status, last_reset)
        VALUES (?, ?, ?, ?, ?, 0, 0, 0, 0, 'Pending', CURRENT_TIMESTAMP)
      `);

      for (const employee of employees) {
        upsertPayroll.run(
          employee.id,
          employee.name,
          employee.salary,
          employee.salary,
          currentMonth,
        );
      }

      // Mark advances as paid
      this.db
        .prepare('UPDATE advances SET status = "paid" WHERE status = "active"')
        .run();

      // Log the reset
      this.db
        .prepare("INSERT INTO monthly_resets (month) VALUES (?)")
        .run(currentMonth);
    });

    transaction();
  }

  // Penalty operations
  getPenalties(): Penalty[] {
    if (!this.db) {
      return this.mockData.penalties;
    }
    return this.db
      .prepare("SELECT * FROM penalties ORDER BY created_at DESC")
      .all() as Penalty[];
  }

  addPenalty(penalty: Omit<Penalty, "id" | "created_at">): Penalty {
    if (!this.db) {
      const newPenalty: Penalty = {
        ...penalty,
        id: Math.max(0, ...this.mockData.penalties.map((p) => p.id)) + 1,
        created_at: new Date().toISOString(),
      };
      this.mockData.penalties.push(newPenalty);

      // Update payroll record in mock data
      const currentMonth = new Date().toISOString().slice(0, 7);
      const payrollRecord = this.mockData.payrollRecords.find(
        (record) =>
          record.employee_id === penalty.employee_id &&
          record.month === currentMonth,
      );
      if (payrollRecord) {
        payrollRecord.penalties += penalty.amount;
        payrollRecord.current_salary =
          payrollRecord.base_salary -
          payrollRecord.penalties -
          payrollRecord.advances +
          payrollRecord.raises +
          payrollRecord.overtime_bonus;
      }

      return newPenalty;
    }

    const insert = this.db.prepare(`
      INSERT INTO penalties (employee_id, employee_name, reason, amount, date, type)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    const result = insert.run(
      penalty.employee_id,
      penalty.employee_name,
      penalty.reason,
      penalty.amount,
      penalty.date,
      penalty.type,
    );

    // Update payroll record - First ensure the record exists
    const currentMonth = new Date().toISOString().slice(0, 7);

    // Check if payroll record exists, create if not
    const existingPayroll = this.db
      .prepare(
        "SELECT * FROM payroll_records WHERE employee_id = ? AND month = ?",
      )
      .get(penalty.employee_id, currentMonth);

    if (!existingPayroll) {
      // Create payroll record if it doesn't exist
      const employee = this.getEmployeeById(penalty.employee_id);
      if (employee) {
        this.db
          .prepare(
            `
            INSERT INTO payroll_records 
            (employee_id, employee_name, base_salary, current_salary, month, penalties, raises, advances, overtime_bonus, status)
            VALUES (?, ?, ?, ?, ?, ?, 0, 0, 0, 'Pending')
          `,
          )
          .run(
            employee.id,
            employee.name,
            employee.salary,
            employee.salary - penalty.amount,
            currentMonth,
            penalty.amount,
          );
      }
    } else {
      // Update existing payroll record
      this.db
        .prepare(
          `
        UPDATE payroll_records 
        SET penalties = penalties + ?, 
            current_salary = base_salary - (penalties + ?) - advances + raises + overtime_bonus
        WHERE employee_id = ? AND month = ?
      `,
        )
        .run(penalty.amount, penalty.amount, penalty.employee_id, currentMonth);
    }

    return this.db
      .prepare("SELECT * FROM penalties WHERE id = ?")
      .get(result.lastInsertRowid) as Penalty;
  }

  deletePenalty(id: number): boolean {
    if (!this.db) {
      const penaltyIndex = this.mockData.penalties.findIndex(
        (p) => p.id === id,
      );
      if (penaltyIndex === -1) return false;

      const penalty = this.mockData.penalties[penaltyIndex];
      this.mockData.penalties.splice(penaltyIndex, 1);

      // Update payroll record in mock data
      const currentMonth = new Date().toISOString().slice(0, 7);
      const payrollRecord = this.mockData.payrollRecords.find(
        (record) =>
          record.employee_id === penalty.employee_id &&
          record.month === currentMonth,
      );
      if (payrollRecord) {
        payrollRecord.penalties -= penalty.amount;
        payrollRecord.current_salary =
          payrollRecord.base_salary -
          payrollRecord.penalties -
          payrollRecord.advances +
          payrollRecord.raises +
          payrollRecord.overtime_bonus;
      }

      return true;
    }

    // Get penalty details before deletion
    const penalty = this.db
      .prepare("SELECT * FROM penalties WHERE id = ?")
      .get(id) as Penalty;

    if (!penalty) return false;

    const result = this.db
      .prepare("DELETE FROM penalties WHERE id = ?")
      .run(id);

    if (result.changes > 0) {
      // Update payroll record
      const currentMonth = new Date().toISOString().slice(0, 7);
      this.db
        .prepare(
          `
        UPDATE payroll_records 
        SET penalties = penalties - ?, current_salary = base_salary - penalties - advances + raises + overtime_bonus
        WHERE employee_id = ? AND month = ?
      `,
        )
        .run(penalty.amount, penalty.employee_id, currentMonth);
    }

    return result.changes > 0;
  }

  // Advance operations
  getAdvances(): Advance[] {
    if (!this.db) {
      return this.mockData.advances;
    }
    return this.db
      .prepare("SELECT * FROM advances ORDER BY created_at DESC")
      .all() as Advance[];
  }

  addAdvance(advance: Omit<Advance, "id" | "created_at">): Advance {
    if (!this.db) {
      const newAdvance: Advance = {
        ...advance,
        id: Math.max(0, ...this.mockData.advances.map((a) => a.id)) + 1,
        created_at: new Date().toISOString(),
      };
      this.mockData.advances.push(newAdvance);

      // Update payroll record in mock data
      const currentMonth = new Date().toISOString().slice(0, 7);
      const payrollRecord = this.mockData.payrollRecords.find(
        (record) =>
          record.employee_id === advance.employee_id &&
          record.month === currentMonth,
      );
      if (payrollRecord) {
        payrollRecord.advances += advance.amount;
        payrollRecord.current_salary =
          payrollRecord.base_salary -
          payrollRecord.penalties -
          payrollRecord.advances +
          payrollRecord.raises +
          payrollRecord.overtime_bonus;
      }

      return newAdvance;
    }

    const insert = this.db.prepare(`
      INSERT INTO advances (employee_id, employee_name, amount, reason, date, month, status)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    const result = insert.run(
      advance.employee_id,
      advance.employee_name,
      advance.amount,
      advance.reason,
      advance.date,
      advance.month,
      advance.status,
    );

    // Update payroll record
    const currentMonth = new Date().toISOString().slice(0, 7);
    this.db
      .prepare(
        `
      UPDATE payroll_records 
      SET advances = advances + ?, current_salary = base_salary - penalties - advances + raises + overtime_bonus
      WHERE employee_id = ? AND month = ?
    `,
      )
      .run(advance.amount, advance.employee_id, currentMonth);

    return this.db
      .prepare("SELECT * FROM advances WHERE id = ?")
      .get(result.lastInsertRowid) as Advance;
  }

  deleteAdvance(id: number): boolean {
    if (!this.db) {
      const advanceIndex = this.mockData.advances.findIndex((a) => a.id === id);
      if (advanceIndex === -1) return false;

      const advance = this.mockData.advances[advanceIndex];
      this.mockData.advances.splice(advanceIndex, 1);

      // Update payroll record in mock data
      const currentMonth = new Date().toISOString().slice(0, 7);
      const payrollRecord = this.mockData.payrollRecords.find(
        (record) =>
          record.employee_id === advance.employee_id &&
          record.month === currentMonth,
      );
      if (payrollRecord) {
        payrollRecord.advances -= advance.amount;
        payrollRecord.current_salary =
          payrollRecord.base_salary -
          payrollRecord.penalties -
          payrollRecord.advances +
          payrollRecord.raises +
          payrollRecord.overtime_bonus;
      }

      return true;
    }

    // Get advance details before deletion
    const advance = this.db
      .prepare("SELECT * FROM advances WHERE id = ?")
      .get(id) as Advance;

    if (!advance) return false;

    const result = this.db.prepare("DELETE FROM advances WHERE id = ?").run(id);

    if (result.changes > 0) {
      // Update payroll record
      const currentMonth = new Date().toISOString().slice(0, 7);
      this.db
        .prepare(
          `
        UPDATE payroll_records 
        SET advances = advances - ?, current_salary = base_salary - penalties - advances + raises + overtime_bonus
        WHERE employee_id = ? AND month = ?
      `,
        )
        .run(advance.amount, advance.employee_id, currentMonth);
    }

    return result.changes > 0;
  }

  // Holiday operations
  getHolidays(): Holiday[] {
    if (!this.db) {
      return this.mockData.holidays;
    }
    return this.db
      .prepare("SELECT * FROM holidays ORDER BY date")
      .all() as Holiday[];
  }

  addHoliday(holiday: Omit<Holiday, "id" | "created_at">): Holiday {
    if (!this.db) {
      const newHoliday: Holiday = {
        ...holiday,
        id: Math.max(0, ...this.mockData.holidays.map((h) => h.id)) + 1,
        created_at: new Date().toISOString(),
      };
      this.mockData.holidays.push(newHoliday);
      return newHoliday;
    }

    const insert = this.db.prepare(
      "INSERT INTO holidays (name, date) VALUES (?, ?)",
    );
    const result = insert.run(holiday.name, holiday.date);
    return this.db
      .prepare("SELECT * FROM holidays WHERE id = ?")
      .get(result.lastInsertRowid) as Holiday;
  }

  removeHoliday(id: number): void {
    if (!this.db) {
      const holidayIndex = this.mockData.holidays.findIndex((h) => h.id === id);
      if (holidayIndex !== -1) {
        this.mockData.holidays.splice(holidayIndex, 1);
      }
      return;
    }
    this.db.prepare("DELETE FROM holidays WHERE id = ?").run(id);
  }

  // Settings operations
  getSettings(): any {
    if (!this.db) {
      return {
        workingDays: this.mockData.settings.workingDays || {},
        workingHours: this.mockData.settings.workingHours || {},
        attendanceRules: this.mockData.settings.attendanceRules || {},
        layoutSettings: this.mockData.settings.layoutSettings || {
          darkMode: false,
          rtlMode: false,
        },
        holidays: this.getHolidays(),
      };
    }

    const settings = this.db
      .prepare("SELECT key, value FROM settings")
      .all() as Settings[];
    const result: any = {};

    for (const setting of settings) {
      try {
        result[setting.key] = JSON.parse(setting.value);
      } catch {
        result[setting.key] = setting.value;
      }
    }

    return {
      workingDays: result.working_days || {},
      workingHours: result.working_hours || {},
      attendanceRules: result.attendance_rules || {},
      layoutSettings: result.layout_settings || {
        darkMode: false,
        rtlMode: false,
      },
      holidays: this.getHolidays(),
    };
  }

  saveSettings(settings: any): void {
    if (!this.db) {
      if (settings.workingDays) {
        this.mockData.settings.workingDays = settings.workingDays;
      }
      if (settings.workingHours) {
        this.mockData.settings.workingHours = settings.workingHours;
      }
      if (settings.attendanceRules) {
        this.mockData.settings.attendanceRules = settings.attendanceRules;
      }
      if (settings.layoutSettings) {
        this.mockData.settings.layoutSettings = settings.layoutSettings;
      }
      return;
    }

    const upsert = this.db.prepare(
      "INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)",
    );

    const transaction = this.db.transaction(() => {
      if (settings.workingDays) {
        upsert.run("working_days", JSON.stringify(settings.workingDays));
      }
      if (settings.workingHours) {
        upsert.run("working_hours", JSON.stringify(settings.workingHours));
      }
      if (settings.attendanceRules) {
        upsert.run(
          "attendance_rules",
          JSON.stringify(settings.attendanceRules),
        );
      }
      if (settings.layoutSettings) {
        upsert.run("layout_settings", JSON.stringify(settings.layoutSettings));
      }
    });

    transaction();
  }

  // Service operations
  getServices(): Service[] {
    if (!this.db) {
      return this.mockData.services;
    }
    return this.db
      .prepare("SELECT * FROM services ORDER BY name")
      .all() as Service[];
  }

  getServiceById(id: number): Service | null {
    if (!this.db) {
      return (
        this.mockData.services.find((service) => service.id === id) || null
      );
    }
    return (
      (this.db
        .prepare("SELECT * FROM services WHERE id = ?")
        .get(id) as Service) || null
    );
  }

  addService(
    service: Omit<Service, "id" | "created_at" | "updated_at">,
  ): Service {
    if (!this.db) {
      const newService: Service = {
        ...service,
        id: Math.max(0, ...this.mockData.services.map((s) => s.id)) + 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      this.mockData.services.push(newService);
      return newService;
    }

    const insert = this.db.prepare(`
      INSERT INTO services (name, duration, price, description)
      VALUES (?, ?, ?, ?)
    `);

    const result = insert.run(
      service.name,
      service.duration,
      service.price,
      service.description || null,
    );

    return this.getServiceById(result.lastInsertRowid as number)!;
  }

  updateService(id: number, updates: Partial<Service>): Service | null {
    if (!this.db) {
      const serviceIndex = this.mockData.services.findIndex((s) => s.id === id);
      if (serviceIndex === -1) return null;

      this.mockData.services[serviceIndex] = {
        ...this.mockData.services[serviceIndex],
        ...updates,
        updated_at: new Date().toISOString(),
      };
      return this.mockData.services[serviceIndex];
    }

    const fields = Object.keys(updates)
      .filter((key) => !["id", "created_at", "updated_at"].includes(key))
      .map((key) => `${key} = ?`)
      .join(", ");
    const values = Object.entries(updates)
      .filter(([key]) => !["id", "created_at", "updated_at"].includes(key))
      .map(([, value]) => value);

    if (fields.length === 0) return this.getServiceById(id);

    const update = this.db.prepare(
      `UPDATE services SET ${fields} WHERE id = ?`,
    );
    update.run(...values, id);

    return this.getServiceById(id);
  }

  deleteService(id: number): boolean {
    if (!this.db) {
      const serviceIndex = this.mockData.services.findIndex((s) => s.id === id);
      if (serviceIndex === -1) return false;
      this.mockData.services.splice(serviceIndex, 1);
      return true;
    }

    const result = this.db.prepare("DELETE FROM services WHERE id = ?").run(id);
    return result.changes > 0;
  }

  // Package operations
  getPackages(): Package[] {
    if (!this.db) {
      return this.mockData.packages;
    }
    return this.db
      .prepare("SELECT * FROM packages ORDER BY name")
      .all() as Package[];
  }

  getPackageById(id: number): Package | null {
    if (!this.db) {
      return this.mockData.packages.find((pkg) => pkg.id === id) || null;
    }
    return (
      (this.db
        .prepare("SELECT * FROM packages WHERE id = ?")
        .get(id) as Package) || null
    );
  }

  getPackageServices(packageId: number): Service[] {
    if (!this.db) {
      const packageServiceIds = this.mockData.packageServices
        .filter((ps) => ps.package_id === packageId)
        .map((ps) => ps.service_id);
      return this.mockData.services.filter((s) =>
        packageServiceIds.includes(s.id),
      );
    }
    return this.db
      .prepare(
        `
        SELECT s.* FROM services s
        JOIN package_services ps ON s.id = ps.service_id
        WHERE ps.package_id = ?
        ORDER BY s.name
      `,
      )
      .all(packageId) as Service[];
  }

  addPackage(
    packageData: Omit<Package, "id" | "created_at" | "updated_at">,
    serviceIds: number[] = [],
  ): Package {
    if (!this.db) {
      const newPackage: Package = {
        ...packageData,
        id: Math.max(0, ...this.mockData.packages.map((p) => p.id)) + 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      this.mockData.packages.push(newPackage);

      // Add package services
      serviceIds.forEach((serviceId) => {
        const newPackageService: PackageService = {
          id:
            Math.max(0, ...this.mockData.packageServices.map((ps) => ps.id)) +
            1,
          package_id: newPackage.id,
          service_id: serviceId,
          created_at: new Date().toISOString(),
        };
        this.mockData.packageServices.push(newPackageService);
      });

      return newPackage;
    }

    const insert = this.db.prepare(`
      INSERT INTO packages (name, price, description)
      VALUES (?, ?, ?)
    `);

    const result = insert.run(
      packageData.name,
      packageData.price,
      packageData.description || null,
    );

    const packageId = result.lastInsertRowid as number;

    // Add package services
    if (serviceIds.length > 0) {
      const insertPackageService = this.db.prepare(`
        INSERT INTO package_services (package_id, service_id)
        VALUES (?, ?)
      `);

      const transaction = this.db.transaction(() => {
        for (const serviceId of serviceIds) {
          insertPackageService.run(packageId, serviceId);
        }
      });
      transaction();
    }

    return this.getPackageById(packageId)!;
  }

  updatePackage(
    id: number,
    updates: Partial<Package>,
    serviceIds?: number[],
  ): Package | null {
    if (!this.db) {
      const packageIndex = this.mockData.packages.findIndex((p) => p.id === id);
      if (packageIndex === -1) return null;

      this.mockData.packages[packageIndex] = {
        ...this.mockData.packages[packageIndex],
        ...updates,
        updated_at: new Date().toISOString(),
      };

      if (serviceIds !== undefined) {
        // Remove existing package services
        this.mockData.packageServices = this.mockData.packageServices.filter(
          (ps) => ps.package_id !== id,
        );
        // Add new package services
        serviceIds.forEach((serviceId) => {
          const newPackageService: PackageService = {
            id:
              Math.max(0, ...this.mockData.packageServices.map((ps) => ps.id)) +
              1,
            package_id: id,
            service_id: serviceId,
            created_at: new Date().toISOString(),
          };
          this.mockData.packageServices.push(newPackageService);
        });
      }

      return this.mockData.packages[packageIndex];
    }

    const fields = Object.keys(updates)
      .filter((key) => !["id", "created_at", "updated_at"].includes(key))
      .map((key) => `${key} = ?`)
      .join(", ");
    const values = Object.entries(updates)
      .filter(([key]) => !["id", "created_at", "updated_at"].includes(key))
      .map(([, value]) => value);

    if (fields.length > 0) {
      const update = this.db.prepare(
        `UPDATE packages SET ${fields} WHERE id = ?`,
      );
      update.run(...values, id);
    }

    if (serviceIds !== undefined) {
      // Update package services
      const transaction = this.db.transaction(() => {
        // Remove existing services
        this.db
          .prepare("DELETE FROM package_services WHERE package_id = ?")
          .run(id);
        // Add new services
        if (serviceIds.length > 0) {
          const insertPackageService = this.db.prepare(`
            INSERT INTO package_services (package_id, service_id)
            VALUES (?, ?)
          `);
          for (const serviceId of serviceIds) {
            insertPackageService.run(id, serviceId);
          }
        }
      });
      transaction();
    }

    return this.getPackageById(id);
  }

  deletePackage(id: number): boolean {
    if (!this.db) {
      const packageIndex = this.mockData.packages.findIndex((p) => p.id === id);
      if (packageIndex === -1) return false;
      this.mockData.packages.splice(packageIndex, 1);
      // Remove package services
      this.mockData.packageServices = this.mockData.packageServices.filter(
        (ps) => ps.package_id !== id,
      );
      return true;
    }

    const result = this.db.prepare("DELETE FROM packages WHERE id = ?").run(id);
    return result.changes > 0;
  }

  // Dress operations
  getDresses(): Dress[] {
    if (!this.db) {
      return this.mockData.dresses;
    }
    return this.db
      .prepare("SELECT * FROM dresses ORDER BY name")
      .all() as Dress[];
  }

  getDressById(id: number): Dress | null {
    if (!this.db) {
      return this.mockData.dresses.find((dress) => dress.id === id) || null;
    }
    return (
      (this.db
        .prepare("SELECT * FROM dresses WHERE id = ?")
        .get(id) as Dress) || null
    );
  }

  searchDresses(filters: {
    status?: string;
    colors?: string[];
    searchTerm?: string;
  }): Dress[] {
    let dresses = this.getDresses();

    if (filters.status) {
      dresses = dresses.filter((dress) => dress.status === filters.status);
    }

    if (filters.colors && filters.colors.length > 0) {
      dresses = dresses.filter((dress) => {
        const dressColors = JSON.parse(dress.colors);
        return filters.colors!.some((color) => dressColors.includes(color));
      });
    }

    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase();
      dresses = dresses.filter(
        (dress) =>
          dress.name.toLowerCase().includes(term) ||
          (dress.description && dress.description.toLowerCase().includes(term)),
      );
    }

    return dresses;
  }

  addDress(dress: Omit<Dress, "id" | "created_at" | "updated_at">): Dress {
    if (!this.db) {
      const newDress: Dress = {
        ...dress,
        id: Math.max(0, ...this.mockData.dresses.map((d) => d.id)) + 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      this.mockData.dresses.push(newDress);
      return newDress;
    }

    const insert = this.db.prepare(`
      INSERT INTO dresses (name, rental_price, status, colors, photos, description)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    const result = insert.run(
      dress.name,
      dress.rental_price,
      dress.status,
      dress.colors,
      dress.photos,
      dress.description || null,
    );

    return this.getDressById(result.lastInsertRowid as number)!;
  }

  updateDress(id: number, updates: Partial<Dress>): Dress | null {
    if (!this.db) {
      const dressIndex = this.mockData.dresses.findIndex((d) => d.id === id);
      if (dressIndex === -1) return null;

      this.mockData.dresses[dressIndex] = {
        ...this.mockData.dresses[dressIndex],
        ...updates,
        updated_at: new Date().toISOString(),
      };
      return this.mockData.dresses[dressIndex];
    }

    const fields = Object.keys(updates)
      .filter((key) => !["id", "created_at", "updated_at"].includes(key))
      .map((key) => `${key} = ?`)
      .join(", ");
    const values = Object.entries(updates)
      .filter(([key]) => !["id", "created_at", "updated_at"].includes(key))
      .map(([, value]) => value);

    if (fields.length === 0) return this.getDressById(id);

    const update = this.db.prepare(`UPDATE dresses SET ${fields} WHERE id = ?`);
    update.run(...values, id);

    return this.getDressById(id);
  }

  deleteDress(id: number): boolean {
    if (!this.db) {
      const dressIndex = this.mockData.dresses.findIndex((d) => d.id === id);
      if (dressIndex === -1) return false;
      this.mockData.dresses.splice(dressIndex, 1);
      return true;
    }

    const result = this.db.prepare("DELETE FROM dresses WHERE id = ?").run(id);
    return result.changes > 0;
  }

  // Customer operations
  getCustomers(): Customer[] {
    if (!this.db) {
      return this.mockData.customers;
    }
    return this.db
      .prepare("SELECT * FROM customers ORDER BY name")
      .all() as Customer[];
  }

  getCustomerById(id: number): Customer | null {
    if (!this.db) {
      return (
        this.mockData.customers.find((customer) => customer.id === id) || null
      );
    }
    return (
      (this.db
        .prepare("SELECT * FROM customers WHERE id = ?")
        .get(id) as Customer) || null
    );
  }

  addCustomer(
    customer: Omit<Customer, "id" | "created_at" | "updated_at">,
  ): Customer {
    if (!this.db) {
      const newCustomer: Customer = {
        ...customer,
        id: Math.max(0, ...this.mockData.customers.map((c) => c.id)) + 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      this.mockData.customers.push(newCustomer);
      return newCustomer;
    }

    const insert = this.db.prepare(`
      INSERT INTO customers (name, phone, email, address, notes, instagram, facebook, twitter, total_bookings, total_spent, last_visit, favorite_services)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = insert.run(
      customer.name,
      customer.phone || null,
      customer.email || null,
      customer.address || null,
      customer.notes || null,
      customer.instagram || null,
      customer.facebook || null,
      customer.twitter || null,
      customer.total_bookings,
      customer.total_spent,
      customer.last_visit || null,
      customer.favorite_services || JSON.stringify([]),
    );

    return this.getCustomerById(result.lastInsertRowid as number)!;
  }

  updateCustomer(id: number, updates: Partial<Customer>): Customer | null {
    if (!this.db) {
      const customerIndex = this.mockData.customers.findIndex(
        (c) => c.id === id,
      );
      if (customerIndex === -1) return null;

      this.mockData.customers[customerIndex] = {
        ...this.mockData.customers[customerIndex],
        ...updates,
        updated_at: new Date().toISOString(),
      };
      return this.mockData.customers[customerIndex];
    }

    const fields = Object.keys(updates)
      .filter((key) => !["id", "created_at", "updated_at"].includes(key))
      .map((key) => `${key} = ?`)
      .join(", ");
    const values = Object.entries(updates)
      .filter(([key]) => !["id", "created_at", "updated_at"].includes(key))
      .map(([, value]) => value);

    if (fields.length === 0) return this.getCustomerById(id);

    const update = this.db.prepare(
      `UPDATE customers SET ${fields} WHERE id = ?`,
    );
    update.run(...values, id);

    return this.getCustomerById(id);
  }

  deleteCustomer(id: number): boolean {
    if (!this.db) {
      const customerIndex = this.mockData.customers.findIndex(
        (c) => c.id === id,
      );
      if (customerIndex === -1) return false;
      this.mockData.customers.splice(customerIndex, 1);
      return true;
    }

    const result = this.db
      .prepare("DELETE FROM customers WHERE id = ?")
      .run(id);
    return result.changes > 0;
  }

  // Booking operations
  getBookings(): Booking[] {
    if (!this.db) {
      return this.mockData.bookings;
    }
    return this.db
      .prepare("SELECT * FROM bookings ORDER BY date DESC, start_time")
      .all() as Booking[];
  }

  getBookingById(id: number): Booking | null {
    if (!this.db) {
      return (
        this.mockData.bookings.find((booking) => booking.id === id) || null
      );
    }
    return (
      (this.db
        .prepare("SELECT * FROM bookings WHERE id = ?")
        .get(id) as Booking) || null
    );
  }

  getBookingsByDate(date: string): Booking[] {
    if (!this.db) {
      return this.mockData.bookings.filter((booking) => booking.date === date);
    }
    return this.db
      .prepare("SELECT * FROM bookings WHERE date = ? ORDER BY start_time")
      .all(date) as Booking[];
  }

  getBookingsByCustomer(customerId: number): Booking[] {
    if (!this.db) {
      return this.mockData.bookings.filter(
        (booking) => booking.customer_id === customerId,
      );
    }
    return this.db
      .prepare(
        "SELECT * FROM bookings WHERE customer_id = ? ORDER BY date DESC",
      )
      .all(customerId) as Booking[];
  }

  addBooking(
    booking: Omit<Booking, "id" | "created_at" | "updated_at">,
  ): Booking {
    if (!this.db) {
      const newBooking: Booking = {
        ...booking,
        id: Math.max(0, ...this.mockData.bookings.map((b) => b.id)) + 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      this.mockData.bookings.push(newBooking);

      // Update dress status if this booking includes dress rentals
      this.updateDressStatusFromBookings();

      return newBooking;
    }

    const insert = this.db.prepare(`
      INSERT INTO bookings (title, notes, date, customer_id, customer_name, employee_ids, start_time, end_time, service_ids, package_ids, dress_ids, dress_rental_start_date, dress_rental_end_date, total_amount, deposit, discount_percentage, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = insert.run(
      booking.title,
      booking.notes,
      booking.date,
      booking.customer_id,
      booking.customer_name,
      booking.employee_ids,
      booking.start_time,
      booking.end_time,
      booking.service_ids,
      booking.package_ids,
      booking.dress_ids,
      booking.dress_rental_start_date || null,
      booking.dress_rental_end_date || null,
      booking.total_amount,
      booking.deposit,
      booking.discount_percentage,
      booking.status,
    );

    const newBooking = this.getBookingById(result.lastInsertRowid as number)!;

    // Update dress status if this booking includes dress rentals
    this.updateDressStatusFromBookings();

    return newBooking;
  }

  updateBooking(id: number, updates: Partial<Booking>): Booking | null {
    if (!this.db) {
      const bookingIndex = this.mockData.bookings.findIndex((b) => b.id === id);
      if (bookingIndex === -1) return null;

      this.mockData.bookings[bookingIndex] = {
        ...this.mockData.bookings[bookingIndex],
        ...updates,
        updated_at: new Date().toISOString(),
      };

      // Update dress status if dress-related fields were changed
      if (
        updates.dress_ids ||
        updates.dress_rental_start_date ||
        updates.dress_rental_end_date ||
        updates.status
      ) {
        this.updateDressStatusFromBookings();
      }

      return this.mockData.bookings[bookingIndex];
    }

    const fields = Object.keys(updates)
      .filter((key) => !["id", "created_at", "updated_at"].includes(key))
      .map((key) => `${key} = ?`)
      .join(", ");
    const values = Object.entries(updates)
      .filter(([key]) => !["id", "created_at", "updated_at"].includes(key))
      .map(([, value]) => value);

    if (fields.length === 0) return this.getBookingById(id);

    const update = this.db.prepare(
      `UPDATE bookings SET ${fields} WHERE id = ?`,
    );
    update.run(...values, id);

    const updatedBooking = this.getBookingById(id);

    // Update dress status if dress-related fields were changed
    if (
      updates.dress_ids ||
      updates.dress_rental_start_date ||
      updates.dress_rental_end_date ||
      updates.status
    ) {
      this.updateDressStatusFromBookings();
    }

    return updatedBooking;
  }

  deleteBooking(id: number): boolean {
    if (!this.db) {
      const bookingIndex = this.mockData.bookings.findIndex((b) => b.id === id);
      if (bookingIndex === -1) return false;

      const deletedBooking = this.mockData.bookings[bookingIndex];
      this.mockData.bookings.splice(bookingIndex, 1);

      // Update dress status after deletion if the booking had dress rentals
      if (
        deletedBooking.dress_ids &&
        JSON.parse(deletedBooking.dress_ids).length > 0
      ) {
        this.updateDressStatusFromBookings();
      }

      return true;
    }

    // Get booking details before deletion to check if it has dress rentals
    const booking = this.getBookingById(id);
    const result = this.db.prepare("DELETE FROM bookings WHERE id = ?").run(id);

    if (
      result.changes > 0 &&
      booking &&
      booking.dress_ids &&
      JSON.parse(booking.dress_ids).length > 0
    ) {
      // Update dress status after deletion
      this.updateDressStatusFromBookings();
    }

    return result.changes > 0;
  }

  // Helper method to apply absent penalty
  private applyAbsentPenalty(employeeId: number, employeeName: string): void {
    const settings = this.getSettings();
    const absencePenalty = settings.attendanceRules?.absencePenalty || 100;

    const penalty = {
      employee_id: employeeId,
      employee_name: employeeName,
      reason: "Absent from work",
      amount: absencePenalty,
      date: new Date().toISOString().split("T")[0],
      type: "Absence Penalty",
    };

    this.addPenalty(penalty);
  }

  // Utility methods
  getStats(): any {
    if (!this.db) {
      return {
        employees: { count: this.mockData.employees.length },
        attendance_records: { count: this.mockData.attendanceRecords.length },
        payroll_records: { count: this.mockData.payrollRecords.length },
        penalties: { count: this.mockData.penalties.length },
        advances: { count: this.mockData.advances.length },
        holidays: { count: this.mockData.holidays.length },
        services: { count: this.mockData.services.length },
        packages: { count: this.mockData.packages.length },
        dresses: { count: this.mockData.dresses.length },
        customers: { count: this.mockData.customers.length },
        bookings: { count: this.mockData.bookings.length },
      };
    }

    return {
      employees: this.db
        .prepare("SELECT COUNT(*) as count FROM employees")
        .get(),
      attendance_records: this.db
        .prepare("SELECT COUNT(*) as count FROM attendance_records")
        .get(),
      payroll_records: this.db
        .prepare("SELECT COUNT(*) as count FROM payroll_records")
        .get(),
      penalties: this.db
        .prepare("SELECT COUNT(*) as count FROM penalties")
        .get(),
      advances: this.db.prepare("SELECT COUNT(*) as count FROM advances").get(),
      holidays: this.db.prepare("SELECT COUNT(*) as count FROM holidays").get(),
      services: this.db.prepare("SELECT COUNT(*) as count FROM services").get(),
      packages: this.db.prepare("SELECT COUNT(*) as count FROM packages").get(),
      dresses: this.db.prepare("SELECT COUNT(*) as count FROM dresses").get(),
      customers: this.db
        .prepare("SELECT COUNT(*) as count FROM customers")
        .get(),
      bookings: this.db.prepare("SELECT COUNT(*) as count FROM bookings").get(),
    };
  }

  // Utility methods
  isFirstWeekOfMonth(): boolean {
    const today = new Date();
    const dayOfMonth = today.getDate();
    return dayOfMonth <= 7;
  }

  checkAndResetMonthlyPayroll(): void {
    const today = new Date();
    if (today.getDate() === 1) {
      this.resetMonthlyPayroll();
    }
  }

  // Database maintenance
  backup(): Buffer {
    if (!this.db) {
      throw new Error("Database backup not available in browser mode");
    }
    return this.db.backup();
  }

  restore(backup: Buffer): void {
    if (!this.db) {
      throw new Error("Database restore not available in browser mode");
    }
    this.db.restore(backup);
  }

  close(): void {
    if (!this.db) {
      return;
    }
    this.db.close();
  }

  // Dress rental availability methods
  isDressAvailableForPeriod(
    dressId: number,
    startDate: string,
    endDate: string,
    excludeBookingId?: number,
  ): boolean {
    const bookings = this.getBookings().filter(
      (booking) =>
        booking.status !== "Cancelled" &&
        booking.id !== excludeBookingId &&
        booking.dress_ids &&
        JSON.parse(booking.dress_ids).includes(dressId) &&
        booking.dress_rental_start_date &&
        booking.dress_rental_end_date,
    );

    for (const booking of bookings) {
      const bookingStart = new Date(booking.dress_rental_start_date!);
      const bookingEnd = new Date(booking.dress_rental_end_date!);
      const requestStart = new Date(startDate);
      const requestEnd = new Date(endDate);

      // Check for date overlap
      if (
        (requestStart <= bookingEnd && requestEnd >= bookingStart) ||
        (bookingStart <= requestEnd && bookingEnd >= requestStart)
      ) {
        return false; // Dress is not available during this period
      }
    }

    return true; // Dress is available
  }

  getDressAvailabilityForPeriod(
    startDate: string,
    endDate: string,
  ): { dressId: number; available: boolean }[] {
    const dresses = this.getDresses();
    return dresses.map((dress) => ({
      dressId: dress.id,
      available: this.isDressAvailableForPeriod(dress.id, startDate, endDate),
    }));
  }

  getActiveRentalPeriods(dressId: number): {
    bookingId: number;
    startDate: string;
    endDate: string;
    customerName: string;
  }[] {
    const bookings = this.getBookings().filter(
      (booking) =>
        booking.status !== "Cancelled" &&
        booking.dress_ids &&
        JSON.parse(booking.dress_ids).includes(dressId) &&
        booking.dress_rental_start_date &&
        booking.dress_rental_end_date,
    );

    return bookings.map((booking) => ({
      bookingId: booking.id,
      startDate: booking.dress_rental_start_date!,
      endDate: booking.dress_rental_end_date!,
      customerName: booking.customer_name,
    }));
  }

  updateDressStatusFromBookings(): void {
    const dresses = this.getDresses();
    const today = new Date().toISOString().split("T")[0];

    dresses.forEach((dress) => {
      // Check if dress is currently rented
      const activeRentals = this.getActiveRentalPeriods(dress.id).filter(
        (rental) => {
          const startDate = new Date(rental.startDate);
          const endDate = new Date(rental.endDate);
          const currentDate = new Date(today);
          return currentDate >= startDate && currentDate <= endDate;
        },
      );

      let newStatus: "Available" | "Rented" | "Maintenance" = "Available";

      // If dress is in maintenance, keep that status
      if (dress.status === "Maintenance") {
        newStatus = "Maintenance";
      } else if (activeRentals.length > 0) {
        newStatus = "Rented";
      } else {
        newStatus = "Available";
      }

      // Update dress status if it has changed
      if (dress.status !== newStatus) {
        this.updateDress(dress.id, { status: newStatus });
      }
    });
  }

  validateBookingDressAvailability(
    dressIds: number[],
    startDate: string,
    endDate: string,
    excludeBookingId?: number,
  ): { valid: boolean; conflicts: { dressId: number; dressName: string }[] } {
    const conflicts: { dressId: number; dressName: string }[] = [];

    for (const dressId of dressIds) {
      if (
        !this.isDressAvailableForPeriod(
          dressId,
          startDate,
          endDate,
          excludeBookingId,
        )
      ) {
        const dress = this.getDressById(dressId);
        if (dress) {
          conflicts.push({ dressId, dressName: dress.name });
        }
      }
    }

    return {
      valid: conflicts.length === 0,
      conflicts,
    };
  }

  // Get dresses with their current rental status and next availability
  getDressesWithRentalInfo(): (Dress & {
    currentRental?: {
      customerName: string;
      startDate: string;
      endDate: string;
    };
    nextAvailableDate?: string;
  })[] {
    const dresses = this.getDresses();
    const today = new Date().toISOString().split("T")[0];

    return dresses.map((dress) => {
      const activeRentals = this.getActiveRentalPeriods(dress.id);

      // Find current rental
      const currentRental = activeRentals.find((rental) => {
        const startDate = new Date(rental.startDate);
        const endDate = new Date(rental.endDate);
        const currentDate = new Date(today);
        return currentDate >= startDate && currentDate <= endDate;
      });

      // Find next available date
      let nextAvailableDate: string | undefined;
      if (activeRentals.length > 0) {
        const sortedRentals = activeRentals.sort(
          (a, b) =>
            new Date(a.endDate).getTime() - new Date(b.endDate).getTime(),
        );
        const lastRental = sortedRentals[sortedRentals.length - 1];
        const nextDay = new Date(lastRental.endDate);
        nextDay.setDate(nextDay.getDate() + 1);
        nextAvailableDate = nextDay.toISOString().split("T")[0];
      }

      return {
        ...dress,
        currentRental: currentRental
          ? {
              customerName: currentRental.customerName,
              startDate: currentRental.startDate,
              endDate: currentRental.endDate,
            }
          : undefined,
        nextAvailableDate,
      };
    });
  }
}

export default DatabaseManager;
