import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Clock, UserCheck, BarChart3, Users } from "lucide-react";

interface AttendanceStatsProps {
  attendanceRecords: any[];
  employees: any[];
}

const AttendanceStats = ({
  attendanceRecords,
  employees,
}: AttendanceStatsProps) => {
  // Calculate today's stats
  const today = new Date().toISOString().split("T")[0];
  const todayRecords = attendanceRecords.filter((r) => r.date === today);

  const todayStats = {
    totalPresent: todayRecords.filter((r) => r.status !== "Absent").length,
    totalLate: todayRecords.filter((r) => r.status === "Late").length,
    totalAbsent:
      employees.filter((emp) => emp.status === "Active").length -
      todayRecords.length,
    avgHours:
      todayRecords.length > 0
        ? todayRecords.reduce((acc, r) => acc + r.hours, 0) /
          todayRecords.length
        : 0,
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Present Today</CardTitle>
          <UserCheck className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {todayStats.totalPresent}
          </div>
          <p className="text-xs text-muted-foreground">
            Out of {employees.filter((e) => e.status === "Active").length}{" "}
            employees
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Late Arrivals</CardTitle>
          <Clock className="h-4 w-4 text-yellow-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-600">
            {todayStats.totalLate}
          </div>
          <p className="text-xs text-muted-foreground">Penalty applicable</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Absent</CardTitle>
          <Users className="h-4 w-4 text-red-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">
            {todayStats.totalAbsent}
          </div>
          <p className="text-xs text-muted-foreground">Not checked in</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Avg Hours</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {todayStats.avgHours.toFixed(1)}h
          </div>
          <p className="text-xs text-muted-foreground">Standard: 8h</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default AttendanceStats;
