import React from "react";
import { cn } from "@/lib/utils";
import {
  Users,
  Scissors,
  Calendar,
  BarChart3,
  Clock,
  User<PERSON>heck,
  LineChart,
  BookOpen,
  LogOut,
  Settings,
  DollarSign,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

interface SidebarProps {
  userRole?: "admin" | "staff";
  activeSection?: string;
  onNavigate?: (section: string) => void;
  onLogout?: () => void;
}

const Sidebar = ({
  userRole = "admin",
  activeSection = "dashboard",
  onNavigate = () => {},
  onLogout = () => {},
}: SidebarProps) => {
  const adminMenuItems = [
    {
      name: "Dashboard",
      section: "dashboard",
      icon: <BarChart3 className="mr-2 h-5 w-5" />,
    },
    {
      name: "Employee Management",
      section: "employees",
      icon: <Users className="mr-2 h-5 w-5" />,
    },
    {
      name: "Service Management",
      section: "services",
      icon: <Scissors className="mr-2 h-5 w-5" />,
    },
    {
      name: "Booking Management",
      section: "bookings",
      icon: <Calendar className="mr-2 h-5 w-5" />,
    },
    {
      name: "Financial Dashboard",
      section: "finances",
      icon: <BarChart3 className="mr-2 h-5 w-5" />,
    },
    {
      name: "Attendance",
      section: "attendance",
      icon: <UserCheck className="mr-2 h-5 w-5" />,
    },
    {
      name: "Payroll",
      section: "payroll",
      icon: <DollarSign className="mr-2 h-5 w-5" />,
    },
    {
      name: "Customer Management",
      section: "customers",
      icon: <Users className="mr-2 h-5 w-5" />,
    },
  ];

  const staffMenuItems = [
    {
      name: "My Schedule",
      section: "schedule",
      icon: <Clock className="mr-2 h-5 w-5" />,
    },
    {
      name: "Record Attendance",
      section: "attendance",
      icon: <UserCheck className="mr-2 h-5 w-5" />,
    },
    {
      name: "My Performance",
      section: "performance",
      icon: <LineChart className="mr-2 h-5 w-5" />,
    },
    {
      name: "Manage Bookings",
      section: "manage-bookings",
      icon: <BookOpen className="mr-2 h-5 w-5" />,
    },
  ];

  const menuItems = userRole === "admin" ? adminMenuItems : staffMenuItems;

  return (
    <div className="flex h-full w-[280px] flex-col bg-background border-r p-4">
      <div className="flex items-center mb-6">
        <h2 className="text-2xl font-bold">Salon Manager</h2>
      </div>

      <nav className="space-y-1 flex-1">
        {menuItems.map((item) => (
          <Button
            key={item.section}
            variant={activeSection === item.section ? "default" : "ghost"}
            className={cn(
              "w-full justify-start text-left h-10",
              activeSection === item.section
                ? "bg-primary text-primary-foreground"
                : "hover:bg-accent",
            )}
            onClick={() => onNavigate(item.section)}
          >
            {item.icon}
            {item.name}
          </Button>
        ))}
      </nav>

      <Separator className="my-4" />

      <div className="space-y-1">
        <Button
          variant={activeSection === "settings" ? "default" : "ghost"}
          className={cn(
            "w-full justify-start text-left",
            activeSection === "settings"
              ? "bg-primary text-primary-foreground"
              : "hover:bg-accent",
          )}
          onClick={() => onNavigate("settings")}
        >
          <Settings className="mr-2 h-5 w-5" />
          Settings
        </Button>
        <Button
          variant="ghost"
          className="w-full justify-start text-left"
          onClick={onLogout}
        >
          <LogOut className="mr-2 h-5 w-5" />
          Logout
        </Button>
      </div>
    </div>
  );
};

export default Sidebar;
