import React, { useState, useEffect } from "react";
import { Calendar } from "@/components/ui/calendar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Clock, UserCheck, BarChart3, Users } from "lucide-react";
import DatabaseManager from "@/lib/database";
import AttendanceRecords from "./AttendanceRecords";
import AttendanceStats from "./AttendanceStats";
import AttendanceDialogs from "./AttendanceDialogs";
import AttendanceSummary from "./AttendanceSummary";

interface AttendanceManagementProps {
  userRole?: "admin" | "staff";
}

const AttendanceManagement = ({
  userRole = "admin",
}: AttendanceManagementProps) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    new Date(),
  );
  const [attendanceRecords, setAttendanceRecords] = useState<any[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);
  const [settings, setSettings] = useState<any>({});
  const [showMarkAttendanceDialog, setShowMarkAttendanceDialog] =
    useState(false);
  const [showBulkCheckinDialog, setShowBulkCheckinDialog] = useState(false);
  const [showLatePenaltyDialog, setShowLatePenaltyDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);

  const dataManager = DatabaseManager.getInstance();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const employeesData = dataManager.getEmployees();
    const attendanceData = dataManager.getAttendanceRecords();
    const settingsData = dataManager.getSettings();

    setEmployees(employeesData);
    setAttendanceRecords(attendanceData);
    setSettings(settingsData);
  };

  const handleEditRecord = (record: any) => {
    setEditingRecord(record);
    setShowEditDialog(true);
  };

  const refreshData = () => {
    loadData();
  };

  return (
    <div className="space-y-6 p-6 bg-background">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">
          Attendance Management
        </h2>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Clock className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Button onClick={() => setShowMarkAttendanceDialog(true)}>
            <UserCheck className="mr-2 h-4 w-4" />
            Mark Attendance
          </Button>
        </div>
      </div>

      {/* Today's Stats */}
      <AttendanceStats
        attendanceRecords={attendanceRecords}
        employees={employees}
      />

      <div className="grid gap-6 md:grid-cols-3">
        {/* Calendar and Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Select Date</CardTitle>
            <CardDescription>View attendance for specific date</CardDescription>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="rounded-md border"
            />
            <Separator className="my-4" />
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Quick Actions</h4>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setShowBulkCheckinDialog(true)}
                >
                  <Clock className="mr-2 h-4 w-4" />
                  Bulk Check-in
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setShowLatePenaltyDialog(true)}
                >
                  <UserCheck className="mr-2 h-4 w-4" />
                  Mark Late Penalty
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                >
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Generate Report
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Attendance Records */}
        <AttendanceRecords
          selectedDate={selectedDate}
          attendanceRecords={attendanceRecords}
          employees={employees}
          onEditRecord={handleEditRecord}
        />
      </div>

      {/* Monthly Summary */}
      <AttendanceSummary
        employees={employees}
        attendanceRecords={attendanceRecords}
      />

      {/* All Dialogs */}
      <AttendanceDialogs
        showMarkAttendanceDialog={showMarkAttendanceDialog}
        setShowMarkAttendanceDialog={setShowMarkAttendanceDialog}
        showBulkCheckinDialog={showBulkCheckinDialog}
        setShowBulkCheckinDialog={setShowBulkCheckinDialog}
        showLatePenaltyDialog={showLatePenaltyDialog}
        setShowLatePenaltyDialog={setShowLatePenaltyDialog}
        showEditDialog={showEditDialog}
        setShowEditDialog={setShowEditDialog}
        editingRecord={editingRecord}
        setEditingRecord={setEditingRecord}
        employees={employees}
        attendanceRecords={attendanceRecords}
        settings={settings}
        selectedDate={selectedDate}
        onRefreshData={refreshData}
      />
    </div>
  );
};

export default AttendanceManagement;
