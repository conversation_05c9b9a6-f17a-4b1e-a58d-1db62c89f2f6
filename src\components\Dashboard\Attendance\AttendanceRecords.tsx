import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface AttendanceRecordsProps {
  selectedDate: Date | undefined;
  attendanceRecords: any[];
  employees: any[];
  onEditRecord: (record: any) => void;
}

const AttendanceRecords = ({
  selectedDate,
  attendanceRecords,
  employees,
  onEditRecord,
}: AttendanceRecordsProps) => {
  const [selectedEmployee, setSelectedEmployee] = useState("all");

  const formatDateForComparison = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  // Create a comprehensive list that includes all employees
  const createEmployeeRecords = () => {
    const employeeMap = new Map();

    // First, add all employees to the map with default values
    employees.forEach((employee) => {
      const key = `${employee.name}_${selectedDate ? formatDateForComparison(selectedDate) : "all"}`;
      employeeMap.set(key, {
        id: `${employee.id}_${selectedDate ? formatDateForComparison(selectedDate) : "default"}`,
        employee_id: employee.id,
        employee_name: employee.name,
        date: selectedDate
          ? formatDateForComparison(selectedDate)
          : new Date().toISOString().split("T")[0],
        check_in: "-",
        check_out: "-",
        status: "Absent",
        hours: 0,
        late_minutes: 0,
        overtime: 0,
        notes: "No record found",
      });
    });

    // Then, update with actual attendance records
    attendanceRecords.forEach((record) => {
      const key = `${record.employee_name}_${record.date}`;
      if (employeeMap.has(key)) {
        employeeMap.set(key, record);
      }
    });

    return Array.from(employeeMap.values());
  };

  const allEmployeeRecords = createEmployeeRecords();

  const filteredRecords = allEmployeeRecords.filter((record) => {
    const matchesEmployee =
      selectedEmployee === "all" || record.employee_name === selectedEmployee;
    const matchesDate = selectedDate
      ? record.date === formatDateForComparison(selectedDate)
      : true;
    return matchesEmployee && matchesDate;
  });

  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Attendance Records</CardTitle>
        <CardDescription>
          {selectedDate
            ? `Records for ${selectedDate.toLocaleDateString()}`
            : "All records"}
        </CardDescription>
        <div className="flex items-center space-x-2 mt-4">
          <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select employee" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Employees</SelectItem>
              {employees.map((employee) => (
                <SelectItem key={employee.id} value={employee.name}>
                  {employee.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Employee</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Check In</TableHead>
              <TableHead>Check Out</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Hours</TableHead>
              <TableHead>Late (min)</TableHead>
              <TableHead>Overtime (min)</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredRecords.map((record) => (
              <TableRow key={record.id}>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center">
                      <span className="text-xs font-medium">
                        {record.employee_name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </span>
                    </div>
                    <span className="font-medium">{record.employee_name}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {new Date(record.date).toLocaleDateString()}
                </TableCell>
                <TableCell>{record.check_in}</TableCell>
                <TableCell>{record.check_out}</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      record.status === "Present"
                        ? "default"
                        : record.status === "Late"
                          ? "secondary"
                          : "destructive"
                    }
                  >
                    {record.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-1">
                    <span>{record.hours}h</span>
                    {record.overtime > 0 && (
                      <Badge variant="outline" className="text-xs">
                        +{record.overtime}h OT
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {record.late_minutes > 0 ? (
                    <span className="text-red-600 font-medium">
                      {record.late_minutes}
                    </span>
                  ) : (
                    <span className="text-green-600">0</span>
                  )}
                </TableCell>
                <TableCell>
                  {(() => {
                    // Calculate overtime minutes based on check-out time vs 5 PM
                    if (record.check_out && record.check_out !== "-") {
                      const checkOutTime = new Date(
                        `2000-01-01 ${record.check_out}`,
                      );
                      const endWorkTime = new Date(`2000-01-01 17:00`); // 5 PM
                      const overtimeMs =
                        checkOutTime.getTime() - endWorkTime.getTime();
                      const overtimeMinutes =
                        overtimeMs > 0
                          ? Math.floor(overtimeMs / (1000 * 60))
                          : 0;

                      return overtimeMinutes > 0 ? (
                        <span className="text-blue-600 font-medium">
                          {overtimeMinutes}
                        </span>
                      ) : (
                        <span className="text-muted-foreground">0</span>
                      );
                    }
                    return <span className="text-muted-foreground">0</span>;
                  })()}
                </TableCell>
                <TableCell>
                  <div className="flex space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEditRecord(record)}
                      disabled={record.check_in === "-"}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={record.check_in === "-"}
                    >
                      View
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default AttendanceRecords;
