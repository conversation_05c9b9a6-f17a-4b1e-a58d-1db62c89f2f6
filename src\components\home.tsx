import React from "react";
import DashboardLayout from "./Dashboard/DashboardLayout";

interface User {
  id: string;
  name: string;
  role: "admin" | "staff";
  avatar?: string;
}

const Home = () => {
  // Mock admin user - login disabled, direct access to admin dashboard
  const currentUser: User = {
    id: "1",
    name: "Admin User",
    role: "admin",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=admin",
  };

  const handleLogout = () => {
    // For now, just reload the page since we're bypassing login
    window.location.reload();
  };

  return <DashboardLayout user={currentUser} onLogout={handleLogout} />;
};

export default Home;
