import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface AttendanceSummaryProps {
  employees: any[];
  attendanceRecords: any[];
}

const AttendanceSummary = ({
  employees,
  attendanceRecords,
}: AttendanceSummaryProps) => {
  const currentMonth = new Date().toLocaleDateString("en-US", {
    month: "long",
    year: "numeric",
  });

  // Calculate working days in current month (simplified to 22 days)
  const workingDaysInMonth = 22;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Monthly Attendance Summary</CardTitle>
        <CardDescription>{currentMonth} attendance overview</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          {employees.map((employee) => {
            const employeeRecords = attendanceRecords.filter(
              (r) => r.employee_name === employee.name,
            );
            const presentDays = employeeRecords.filter(
              (r) => r.status !== "Absent",
            ).length;
            const lateDays = employeeRecords.filter(
              (r) => r.status === "Late",
            ).length;
            const totalHours = employeeRecords.reduce(
              (acc, r) => acc + r.hours,
              0,
            );
            const attendancePercentage =
              (presentDays / workingDaysInMonth) * 100;

            return (
              <div key={employee.id} className="p-4 border rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                    <span className="text-sm font-medium">
                      {employee.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </span>
                  </div>
                  <div>
                    <div className="font-medium text-sm">{employee.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {employee.position}
                    </div>
                  </div>
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Present:</span>
                    <span className="font-medium">
                      {presentDays}/{workingDaysInMonth} days
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Late:</span>
                    <span className="font-medium text-yellow-600">
                      {lateDays} days
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Hours:</span>
                    <span className="font-medium">
                      {totalHours.toFixed(1)}h
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Rate:</span>
                    <span
                      className={`font-medium ${
                        attendancePercentage >= 95
                          ? "text-green-600"
                          : attendancePercentage >= 85
                            ? "text-yellow-600"
                            : "text-red-600"
                      }`}
                    >
                      {attendancePercentage.toFixed(1)}%
                    </span>
                  </div>
                  <Progress value={attendancePercentage} className="h-1 mt-2" />
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default AttendanceSummary;
