import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Users, Search } from "lucide-react";
import DatabaseManager from "@/lib/database";
import EmployeeStats from "./EmployeeStats";
import EmployeeList from "./EmployeeList";
import EmployeeDialogs from "./EmployeeDialogs";
import EmployeeAdvances from "./EmployeeAdvances";

interface EmployeeManagementProps {
  userRole?: "admin" | "staff";
}

const EmployeeManagement = ({
  userRole = "admin",
}: EmployeeManagementProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [employees, setEmployees] = useState<any[]>([]);
  const [advances, setAdvances] = useState<any[]>([]);
  const [showAdvanceDialog, setShowAdvanceDialog] = useState(false);
  const [selectedEmployeeForAdvance, setSelectedEmployeeForAdvance] =
    useState("");
  const [advanceAmount, setAdvanceAmount] = useState(0);
  const [advanceReason, setAdvanceReason] = useState("");
  const [showAddEmployeeDialog, setShowAddEmployeeDialog] = useState(false);
  const [showEmployeeProfile, setShowEmployeeProfile] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<any>(null);
  const [newEmployee, setNewEmployee] = useState({
    name: "",
    position: "",
    status: "Active" as "Active" | "On Leave" | "Terminated",
    salary: 0,
    phone: "",
    email: "",
    hire_date: new Date().toISOString().split("T")[0],
    avatar: "",
  });

  const dataManager = DatabaseManager.getInstance();

  useEffect(() => {
    loadEmployeeData();
  }, []);

  const loadEmployeeData = () => {
    setEmployees(dataManager.getEmployees());
    setAdvances(dataManager.getAdvances());
  };

  const handleAdvanceRequest = () => {
    if (!selectedEmployeeForAdvance || advanceAmount <= 0 || !advanceReason)
      return;

    if (dataManager.isFirstWeekOfMonth()) {
      alert("Advances are not allowed during the first week of the month.");
      return;
    }

    const employee = employees.find(
      (emp) => emp.id.toString() === selectedEmployeeForAdvance,
    );
    if (!employee) return;

    const advance = {
      employee_id: employee.id,
      employee_name: employee.name,
      amount: advanceAmount,
      reason: advanceReason,
      date: new Date().toISOString().split("T")[0],
      month: new Date().toISOString().slice(0, 7),
      status: "active" as const,
    };

    dataManager.addAdvance(advance);
    loadEmployeeData();
    setShowAdvanceDialog(false);
    setSelectedEmployeeForAdvance("");
    setAdvanceAmount(0);
    setAdvanceReason("");
  };

  const handleAddEmployee = () => {
    if (
      !newEmployee.name ||
      !newEmployee.position ||
      !newEmployee.email ||
      newEmployee.salary <= 0
    ) {
      alert("Please fill in all required fields.");
      return;
    }

    const employeeData = {
      ...newEmployee,
      attendance: 0,
      last_attendance: "",
      avatar:
        newEmployee.avatar ||
        `https://api.dicebear.com/7.x/avataaars/svg?seed=${newEmployee.name}`,
    };

    try {
      dataManager.addEmployee(employeeData);
      loadEmployeeData();
      setShowAddEmployeeDialog(false);
      setNewEmployee({
        name: "",
        position: "",
        status: "Active",
        salary: 0,
        phone: "",
        email: "",
        hire_date: new Date().toISOString().split("T")[0],
        avatar: "",
      });
      alert("Employee added successfully!");
    } catch (error) {
      alert("Error adding employee. Please check if email is unique.");
    }
  };

  const handleViewProfile = (employee: any) => {
    setSelectedEmployee(employee);
    setShowEmployeeProfile(true);
  };

  const handleUpdateEmployee = () => {
    if (!selectedEmployee) return;

    try {
      dataManager.updateEmployee(selectedEmployee.id, selectedEmployee);
      loadEmployeeData();
      setShowEmployeeProfile(false);
      alert("Employee updated successfully!");
    } catch (error) {
      alert("Error updating employee.");
    }
  };

  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        if (showEmployeeProfile && selectedEmployee) {
          setSelectedEmployee({ ...selectedEmployee, avatar: result });
        } else {
          setNewEmployee({ ...newEmployee, avatar: result });
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveAvatar = () => {
    if (showEmployeeProfile && selectedEmployee) {
      setSelectedEmployee({ ...selectedEmployee, avatar: "" });
    } else {
      setNewEmployee({ ...newEmployee, avatar: "" });
    }
  };

  const filteredEmployees = employees.filter((employee) => {
    const matchesSearch =
      employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.position.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  return (
    <div className="space-y-6 p-6 bg-background">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">
          Employee Management
        </h2>
        <Button onClick={() => setShowAddEmployeeDialog(true)}>
          <Users className="mr-2 h-4 w-4" />
          Add Employee
        </Button>
      </div>

      {/* Stats Cards */}
      <EmployeeStats employees={employees} />

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Search employees..."
            className="max-w-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Button variant="outline" size="icon">
            <Search className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">All Employees</TabsTrigger>
          <TabsTrigger value="active">
            Active ({employees.filter((e) => e.status === "Active").length})
          </TabsTrigger>
          <TabsTrigger value="inactive">
            On Leave ({employees.filter((e) => e.status === "On Leave").length})
          </TabsTrigger>
          <TabsTrigger value="advances">Advances</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <EmployeeList
            employees={filteredEmployees}
            onViewProfile={handleViewProfile}
          />
        </TabsContent>

        <TabsContent value="active" className="space-y-4">
          <EmployeeList
            employees={filteredEmployees.filter(
              (emp) => emp.status === "Active",
            )}
            onViewProfile={handleViewProfile}
            showDepartment
          />
        </TabsContent>

        <TabsContent value="inactive" className="space-y-4">
          <EmployeeList
            employees={filteredEmployees.filter(
              (emp) => emp.status === "On Leave",
            )}
            onViewProfile={handleViewProfile}
            showLeaveInfo
          />
        </TabsContent>

        <TabsContent value="advances" className="space-y-4">
          <EmployeeAdvances
            employees={employees}
            advances={advances}
            dataManager={dataManager}
            showAdvanceDialog={showAdvanceDialog}
            setShowAdvanceDialog={setShowAdvanceDialog}
            selectedEmployeeForAdvance={selectedEmployeeForAdvance}
            setSelectedEmployeeForAdvance={setSelectedEmployeeForAdvance}
            advanceAmount={advanceAmount}
            setAdvanceAmount={setAdvanceAmount}
            advanceReason={advanceReason}
            setAdvanceReason={setAdvanceReason}
            onAdvanceRequest={handleAdvanceRequest}
            onRefreshData={loadEmployeeData}
          />
        </TabsContent>
      </Tabs>

      {/* All Dialogs */}
      <EmployeeDialogs
        showAddEmployeeDialog={showAddEmployeeDialog}
        setShowAddEmployeeDialog={setShowAddEmployeeDialog}
        showEmployeeProfile={showEmployeeProfile}
        setShowEmployeeProfile={setShowEmployeeProfile}
        selectedEmployee={selectedEmployee}
        setSelectedEmployee={setSelectedEmployee}
        newEmployee={newEmployee}
        setNewEmployee={setNewEmployee}
        onAddEmployee={handleAddEmployee}
        onUpdateEmployee={handleUpdateEmployee}
        onAvatarUpload={handleAvatarUpload}
        onRemoveAvatar={handleRemoveAvatar}
      />
    </div>
  );
};

export default EmployeeManagement;
