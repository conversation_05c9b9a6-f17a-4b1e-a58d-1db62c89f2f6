# Salon Management System - Electron Setup

This application is configured to run as an Electron desktop app with SQLite database storage.

## Development Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Install Electron Dependencies**
   ```bash
   npm install --save-dev electron concurrently wait-on electron-builder
   ```

3. **Run in Development Mode**
   ```bash
   # Start Vite dev server and Electron together
   npm run electron-dev
   ```

   Or run separately:
   ```bash
   # Terminal 1: Start Vite dev server
   npm run dev
   
   # Terminal 2: Start Electron (after Vite is running)
   npm run electron
   ```

## Building for Production

1. **Build the Application**
   ```bash
   npm run build-electron
   ```

2. **Create Distributable**
   ```bash
   npm run dist
   ```

## Database Storage

- **Development**: Database stored in project root as `salon_management.db`
- **Production**: Database stored in user data directory:
  - Windows: `%APPDATA%/salon-management-system/salon_management.db`
  - macOS: `~/Library/Application Support/salon-management-system/salon_management.db`
  - Linux: `~/.config/salon-management-system/salon_management.db`

## Features

- ✅ Real SQLite database with better-sqlite3
- ✅ Offline functionality
- ✅ Desktop application with Electron
- ✅ Data persistence in user data directory
- ✅ Employee management with avatars
- ✅ Attendance tracking
- ✅ Payroll management
- ✅ Settings configuration

## Project Structure

```
├── electron.js              # Electron main process
├── src/
│   ├── lib/
│   │   ├── database.ts       # SQLite database manager
│   │   └── electron-store.ts # Electron utilities
│   └── components/
│       └── Dashboard/        # Main application components
├── dist/                     # Built web assets
└── dist-electron/           # Built Electron distributables
```

## Troubleshooting

1. **Database Issues**: Check that better-sqlite3 is properly installed for your platform
2. **Electron Not Starting**: Ensure all dependencies are installed with `npm install`
3. **Build Errors**: Try clearing node_modules and reinstalling: `rm -rf node_modules && npm install`

## Next Steps

1. Test the application in Electron environment
2. Add more database features as needed
3. Configure auto-updater for production releases
4. Add application icons and branding
