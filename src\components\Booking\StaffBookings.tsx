import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { CalendarIcon, Clock } from "lucide-react";
import { Booking, getStatusColor, getNextStatus } from "@/types/booking";
import DatabaseManager from "@/lib/database";

const StaffBookings: React.FC = () => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);

  const dataManager = DatabaseManager.getInstance();

  useEffect(() => {
    loadBookings();
  }, []);

  const loadBookings = () => {
    setLoading(true);
    try {
      // Load existing bookings from database
      const dbBookings = dataManager.getBookings().map((booking) => ({
        ...booking,
        service_ids: JSON.parse(booking.service_ids || "[]"),
        package_ids: JSON.parse(booking.package_ids || "[]"),
        dress_ids: JSON.parse(booking.dress_ids || "[]"),
        employee_ids: JSON.parse(booking.employee_ids || "[]"),
      }));
      setBookings(dbBookings);
    } catch (error) {
      console.error("Error loading bookings:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = (bookingId: number, newStatus: string) => {
    const success = dataManager.updateBookingStatus(bookingId, newStatus);
    if (success) {
      loadBookings();
    } else {
      alert("Failed to update booking status");
    }
  };

  // Filter bookings by status
  const getBookingsByStatus = (status: string) => {
    const today = new Date();
    const todayString = today.toISOString().split("T")[0];

    switch (status) {
      case "upcoming":
        return bookings.filter(
          (booking) =>
            (booking.status === "Confirmed" || booking.status === "In Progress") &&
            booking.date >= todayString
        );
      case "completed":
        return bookings.filter((booking) => booking.status === "Completed");
      case "cancelled":
        return bookings.filter((booking) => booking.status === "Cancelled");
      default:
        return [];
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return "Tomorrow";
    } else {
      return date.toLocaleDateString("en-US", { month: "short", day: "numeric" });
    }
  };

  const renderBookingCard = (booking: Booking) => (
    <div
      key={booking.id}
      className="flex items-start space-x-4 p-3 rounded-lg border"
    >
      <div className="bg-primary/10 p-2 rounded-md">
        <CalendarIcon className="h-5 w-5 text-primary" />
      </div>
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">{booking.title || "Booking"}</h4>
          <Badge variant={getStatusColor(booking.status)}>
            {formatDate(booking.date)}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">
          Client: {booking.customer_name}
        </p>
        <div className="mt-2 flex items-center text-xs text-muted-foreground">
          <span className="mr-2">
            Time: {booking.start_time || "Not set"}
            {booking.end_time && ` - ${booking.end_time}`}
          </span>
          <span>Amount: ${booking.total_amount}</span>
        </div>
        {booking.notes && (
          <p className="text-xs text-muted-foreground mt-1">{booking.notes}</p>
        )}
      </div>
      <div className="flex space-x-2">
        {booking.status !== "Completed" && booking.status !== "Cancelled" && (
          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              handleStatusChange(booking.id, getNextStatus(booking.status))
            }
          >
            {booking.status === "Confirmed" ? "Start" : "Complete"}
          </Button>
        )}
        {booking.status === "Confirmed" && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleStatusChange(booking.id, "Cancelled")}
          >
            Cancel
          </Button>
        )}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <h2 className="text-3xl font-bold tracking-tight">Manage Bookings</h2>
        <div className="flex items-center justify-center py-8">
          <p className="text-muted-foreground">Loading bookings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-bold tracking-tight">Manage Bookings</h2>

      <Tabs defaultValue="upcoming">
        <TabsList>
          <TabsTrigger value="upcoming">
            Upcoming ({getBookingsByStatus("upcoming").length})
          </TabsTrigger>
          <TabsTrigger value="completed">
            Completed ({getBookingsByStatus("completed").length})
          </TabsTrigger>
          <TabsTrigger value="cancelled">
            Cancelled ({getBookingsByStatus("cancelled").length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upcoming" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Bookings</CardTitle>
              <CardDescription>
                Confirmed and in-progress appointments
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="space-y-4">
                {getBookingsByStatus("upcoming").length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <CalendarIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No upcoming bookings</p>
                  </div>
                ) : (
                  getBookingsByStatus("upcoming")
                    .sort((a, b) => {
                      // Sort by date first, then by time
                      const dateCompare = a.date.localeCompare(b.date);
                      if (dateCompare !== 0) return dateCompare;
                      return a.start_time.localeCompare(b.start_time);
                    })
                    .map(renderBookingCard)
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Completed Bookings</CardTitle>
              <CardDescription>Successfully finished appointments</CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="space-y-4">
                {getBookingsByStatus("completed").length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>No completed bookings</p>
                  </div>
                ) : (
                  getBookingsByStatus("completed")
                    .sort((a, b) => b.date.localeCompare(a.date))
                    .map(renderBookingCard)
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cancelled" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cancelled Bookings</CardTitle>
              <CardDescription>Appointments that were cancelled</CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="space-y-4">
                {getBookingsByStatus("cancelled").length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>No cancelled bookings</p>
                  </div>
                ) : (
                  getBookingsByStatus("cancelled")
                    .sort((a, b) => b.date.localeCompare(a.date))
                    .map(renderBookingCard)
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default StaffBookings;
