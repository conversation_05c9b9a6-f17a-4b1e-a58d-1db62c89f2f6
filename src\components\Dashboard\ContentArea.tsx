import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import {
  CalendarIcon,
  DollarSign,
  Users,
  Package,
  Calendar as CalendarIcon2,
  Clock,
  Search,
  BarChart3,
  <PERSON>r<PERSON>heck,
  Settings,
  Save,
  Plus,
  Trash2,
  Phone,
  Mail,
  MapPin,
  User,
  Eye,
  Edit,
  Star,
  TrendingUp,
  Calendar as CalendarDays,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface ContentAreaProps {
  activeSection?: string;
  userRole?: "admin" | "staff";
}

import DatabaseManager from "@/lib/database";
import BookingManagement from "@/components/Booking/BookingManagement";
import StaffBookings from "@/components/Booking/StaffBookings";
import QuickBooking from "@/components/Booking/QuickBooking";

const ContentArea = ({
  activeSection = "dashboard",
  userRole = "admin",
}: ContentAreaProps) => {
  const [date, setDate] = useState<Date | undefined>(new Date());

  // Render different content based on active section and user role
  const renderContent = () => {
    switch (activeSection) {
      case "dashboard":
        return <DashboardContent userRole={userRole} />;
      case "employees":
        return <EmployeeManagement />;
      case "services":
        return <ServiceManagement />;
      case "bookings":
        return <BookingManagement />;
      case "finances":
        return <FinancialDashboard />;
      case "schedule":
        return <StaffSchedule />;
      case "attendance":
        return <RecordAttendance userRole={userRole} />;
      case "performance":
        return <StaffPerformance />;
      case "manage-bookings":
        return <StaffBookings />;
      case "settings":
        return <SettingsManagement />;
      case "payroll":
        return <PayrollManagement />;
      case "customers":
        return <CustomerManagement />;
      default:
        return <DashboardContent userRole={userRole} />;
    }
  };

  return <div className="flex-1 p-6 bg-background">{renderContent()}</div>;
};

// Dashboard content component
const DashboardContent = ({ userRole }: { userRole: "admin" | "staff" }) => {
  const [dashboardData, setDashboardData] = useState({
    totalRevenue: 0,
    totalBookings: 0,
    activeEmployees: 0,
    popularService: "Loading...",
    weeklyRevenue: [0, 0, 0, 0, 0, 0, 0],
    todayBookings: [] as any[],
  });
  const [selectedPeriod, setSelectedPeriod] = useState("weekly");

  const dataManager = DatabaseManager.getInstance();

  useEffect(() => {
    loadDashboardData();
  }, [selectedPeriod]);

  const loadDashboardData = () => {
    // Load bookings from database
    const bookings = dataManager.getBookings().map((booking) => ({
      ...booking,
      service_ids: JSON.parse(booking.service_ids || "[]"),
      package_ids: JSON.parse(booking.package_ids || "[]"),
      dress_ids: JSON.parse(booking.dress_ids || "[]"),
      employee_ids: JSON.parse(booking.employee_ids || "[]"),
    }));

    // Filter out cancelled bookings for revenue calculation
    const validBookings = bookings.filter(
      (booking: any) => booking.status !== "Cancelled",
    );

    // Calculate total revenue
    const totalRevenue = validBookings.reduce(
      (sum: number, booking: any) => sum + booking.total_amount,
      0,
    );

    // Get total bookings count (excluding cancelled)
    const totalBookings = validBookings.length;

    // Get active employees
    const employees = dataManager.getEmployees();
    const activeEmployees = employees.filter(
      (emp) => emp.status === "Active",
    ).length;

    // Get services to find popular service
    const services = dataManager.getServices();
    const serviceBookingCount: { [key: string]: number } = {};

    validBookings.forEach((booking: any) => {
      booking.service_ids?.forEach((serviceId: number) => {
        const service = services.find((s) => s.id === serviceId);
        if (service) {
          serviceBookingCount[service.name] =
            (serviceBookingCount[service.name] || 0) + 1;
        }
      });
    });

    const popularService =
      Object.keys(serviceBookingCount).length > 0
        ? Object.keys(serviceBookingCount).reduce((a, b) =>
            serviceBookingCount[a] > serviceBookingCount[b] ? a : b,
          )
        : "No bookings yet";

    // Calculate period-based revenue
    const today = new Date();
    let periodRevenue = [];
    let periodLabel = "";

    switch (selectedPeriod) {
      case "daily":
        periodRevenue = Array(7).fill(0);
        periodLabel = "Last 7 Days";
        for (let i = 0; i < 7; i++) {
          const date = new Date(today);
          date.setDate(date.getDate() - (6 - i));
          const dateString = date.toISOString().split("T")[0];
          const dayRevenue = validBookings
            .filter((booking: any) => booking.date === dateString)
            .reduce(
              (sum: number, booking: any) => sum + booking.total_amount,
              0,
            );
          periodRevenue[i] = dayRevenue;
        }
        break;
      case "weekly":
        periodRevenue = Array(4).fill(0);
        periodLabel = "Last 4 Weeks";
        for (let i = 0; i < 4; i++) {
          const startDate = new Date(today);
          startDate.setDate(startDate.getDate() - (i + 1) * 7);
          const endDate = new Date(today);
          endDate.setDate(endDate.getDate() - i * 7);
          const weekRevenue = validBookings
            .filter((booking: any) => {
              const bookingDate = new Date(booking.date);
              return bookingDate >= startDate && bookingDate < endDate;
            })
            .reduce(
              (sum: number, booking: any) => sum + booking.total_amount,
              0,
            );
          periodRevenue[3 - i] = weekRevenue;
        }
        break;
      case "monthly":
        periodRevenue = Array(6).fill(0);
        periodLabel = "Last 6 Months";
        for (let i = 0; i < 6; i++) {
          const date = new Date(today);
          date.setMonth(date.getMonth() - (5 - i));
          const monthString = date.toISOString().slice(0, 7);
          const monthRevenue = validBookings
            .filter((booking: any) => booking.date.startsWith(monthString))
            .reduce(
              (sum: number, booking: any) => sum + booking.total_amount,
              0,
            );
          periodRevenue[i] = monthRevenue;
        }
        break;
      case "yearly":
        periodRevenue = Array(3).fill(0);
        periodLabel = "Last 3 Years";
        for (let i = 0; i < 3; i++) {
          const year = today.getFullYear() - (2 - i);
          const yearRevenue = validBookings
            .filter((booking: any) => booking.date.startsWith(year.toString()))
            .reduce(
              (sum: number, booking: any) => sum + booking.total_amount,
              0,
            );
          periodRevenue[i] = yearRevenue;
        }
        break;
      default:
        periodRevenue = Array(7).fill(0);
        periodLabel = "Last 7 Days";
        for (let i = 0; i < 7; i++) {
          const date = new Date(today);
          date.setDate(date.getDate() - (6 - i));
          const dateString = date.toISOString().split("T")[0];
          const dayRevenue = validBookings
            .filter((booking: any) => booking.date === dateString)
            .reduce(
              (sum: number, booking: any) => sum + booking.total_amount,
              0,
            );
          periodRevenue[i] = dayRevenue;
        }
    }

    // Get today's bookings
    const todayString = today.toISOString().split("T")[0];
    const todayBookings = bookings
      .filter(
        (booking: any) =>
          booking.date === todayString && booking.status !== "Cancelled",
      )
      .slice(0, 3); // Show only first 3

    setDashboardData({
      totalRevenue,
      totalBookings,
      activeEmployees,
      popularService,
      weeklyRevenue: periodRevenue,
      todayBookings,
    });
  };

  const maxWeeklyRevenue = Math.max(...dashboardData.weeklyRevenue, 1);



  const getPeriodLabel = (index: number) => {
    const today = new Date();
    switch (selectedPeriod) {
      case "daily":
        const date = new Date(today);
        date.setDate(date.getDate() - (6 - index));
        return date.toLocaleDateString("en-US", { weekday: "short" });
      case "weekly":
        return `Week ${index + 1}`;
      case "monthly":
        const monthDate = new Date(today);
        monthDate.setMonth(monthDate.getMonth() - (5 - index));
        return monthDate.toLocaleDateString("en-US", { month: "short" });
      case "yearly":
        return (today.getFullYear() - (2 - index)).toString();
      default:
        return "";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={loadDashboardData}>
            <CalendarIcon className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${dashboardData.totalRevenue.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Excluding cancelled bookings
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Bookings
            </CardTitle>
            <CalendarIcon2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData.totalBookings}
            </div>
            <p className="text-xs text-muted-foreground">
              Completed and confirmed
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Employees
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData.activeEmployees}
            </div>
            <p className="text-xs text-muted-foreground">Currently working</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Popular Service
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              {dashboardData.popularService}
            </div>
            <p className="text-xs text-muted-foreground">Most booked service</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions Component */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>Quick Actions</span>
          </CardTitle>
          <CardDescription>Quickly perform common tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <QuickBooking onBookingCreated={loadDashboardData} />
            <Button
              variant="outline"
              className="flex items-center space-x-2 h-12 px-6"
            >
              <Users className="h-5 w-5" />
              <span>Add Customer</span>
            </Button>
            <Button
              variant="outline"
              className="flex items-center space-x-2 h-12 px-6"
            >
              <CalendarIcon className="h-5 w-5" />
              <span>View Calendar</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>
              {selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)}{" "}
              Revenue Overview
            </CardTitle>
            <CardDescription>
              Revenue trends (excluding cancelled bookings)
            </CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <div className="h-[200px] flex items-end justify-between">
              {dashboardData.weeklyRevenue.map((revenue, i) => {
                const height =
                  maxWeeklyRevenue > 0 ? (revenue / maxWeeklyRevenue) * 160 : 0;
                const date = new Date();
                date.setDate(date.getDate() - (6 - i));
                return (
                  <div
                    key={i}
                    className="relative flex flex-col items-center group"
                  >
                    <div className="absolute -top-8 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                      ${revenue.toFixed(0)}
                    </div>
                    <div
                      className="w-12 bg-primary rounded-md transition-all hover:bg-primary/80"
                      style={{ height: `${Math.max(height, 4)}px` }}
                    />
                    <span className="text-xs mt-2">{getPeriodLabel(i)}</span>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Today's Appointments</CardTitle>
            <CardDescription>
              {dashboardData.todayBookings.length} appointments today
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData.todayBookings.length === 0 ? (
                <p className="text-center py-4 text-muted-foreground">
                  No appointments today
                </p>
              ) : (
                dashboardData.todayBookings.map((booking, i) => (
                  <div key={i} className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-primary mr-2" />
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {booking.title || "Booking"} -{" "}
                        {booking.customer_name || "Customer"}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {booking.start_time
                          ? `Today, ${booking.start_time}`
                          : "Time not set"}
                      </p>
                    </div>
                    <Badge
                      variant={
                        booking.status === "Confirmed"
                          ? "default"
                          : booking.status === "In Progress"
                            ? "secondary"
                            : "outline"
                      }
                    >
                      {booking.status}
                    </Badge>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>


    </div>
  );
};

// Payroll Management component
const PayrollManagement = () => {
  const [payrollData, setPayrollData] = useState<any[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);
  const [penalties, setPenalties] = useState<any[]>([]);
  const [advances, setAdvances] = useState<any[]>([]);
  const [attendanceRecords, setAttendanceRecords] = useState<any[]>([]);
  const [settings, setSettings] = useState<any>({});
  const [overtimeBonuses, setOvertimeBonuses] = useState<any[]>([]);

  const dataManager = DatabaseManager.getInstance();

  useEffect(() => {
    loadPayrollData();
    initializePayrollRecords();
  }, []);

  const loadPayrollData = () => {
    const payroll = dataManager.getPayrollRecords();
    const employeesData = dataManager.getEmployees();
    const penaltiesData = dataManager.getPenalties();
    const advancesData = dataManager.getAdvances();
    const attendanceData = dataManager.getAttendanceRecords();
    const settingsData = dataManager.getSettings();

    // Calculate overtime bonuses from attendance records
    const currentMonth = new Date().toISOString().slice(0, 7);
    const overtimeBonusesData = employeesData
      .map((employee) => {
        const monthlyAttendance = attendanceData.filter(
          (r) =>
            r.employee_id === employee.id && r.date.startsWith(currentMonth),
        );
        const totalOvertime = monthlyAttendance.reduce(
          (sum, r) => sum + (r.overtime || 0),
          0,
        );
        const overtimeBonus =
          totalOvertime * (settingsData.attendanceRules?.overtimeReward || 25);

        return {
          id: employee.id,
          employee_id: employee.id,
          employee_name: employee.name,
          overtime_hours: totalOvertime,
          bonus_amount: overtimeBonus,
          month: currentMonth,
          created_at: new Date().toISOString(),
        };
      })
      .filter((bonus) => bonus.overtime_hours > 0);

    setPayrollData(payroll);
    setEmployees(employeesData);
    setPenalties(penaltiesData);
    setAdvances(advancesData);
    setAttendanceRecords(attendanceData);
    setSettings(settingsData);
    setOvertimeBonuses(overtimeBonusesData);
  };

  const initializePayrollRecords = () => {
    const employeesData = dataManager.getEmployees();
    const currentMonth = new Date().toISOString().slice(0, 7);

    employeesData.forEach((employee) => {
      const existingRecord = dataManager.getPayrollByEmployee(
        employee.id,
        currentMonth,
      );
      if (!existingRecord) {
        // Create initial payroll record with calculated current salary
        const calculatedSalary = calculateCurrentSalaryForEmployee(employee.id);
        dataManager.updatePayrollRecord(employee.id, {
          employee_name: employee.name,
          base_salary: employee.salary,
          current_salary: calculatedSalary,
          month: currentMonth,
        });
      }
    });
  };

  const calculateCurrentSalaryForEmployee = (employeeId: number) => {
    const employee = employees.find((emp) => emp.id === employeeId);
    if (!employee) return 0;

    const currentMonth = new Date().toISOString().slice(0, 7);

    // Get penalties for current month
    const employeePenalties = penalties
      .filter(
        (p) => p.employee_id === employeeId && p.date.startsWith(currentMonth),
      )
      .reduce((sum, p) => sum + p.amount, 0);

    // Get advances for current month
    const employeeAdvances = advances
      .filter(
        (a) =>
          a.employee_id === employeeId &&
          a.month === currentMonth &&
          a.status === "active",
      )
      .reduce((sum, a) => sum + a.amount, 0);

    // Get overtime bonus
    const monthlyAttendance = attendanceRecords.filter(
      (r) => r.employee_id === employeeId && r.date.startsWith(currentMonth),
    );
    const totalOvertime = monthlyAttendance.reduce(
      (sum, r) => sum + (r.overtime || 0),
      0,
    );
    const overtimeBonus =
      totalOvertime * (settings.attendanceRules?.overtimeReward || 25);

    // Calculate automatic penalties based on attendance rules
    const lateDays = monthlyAttendance.filter(
      (r) => r.status === "Late",
    ).length;
    const absentDays = monthlyAttendance.filter(
      (r) => r.status === "Absent",
    ).length;

    const lateDaysThreshold = settings.attendanceRules?.lateDaysThreshold || 5;
    const absentDaysThreshold =
      settings.attendanceRules?.absentDaysThreshold || 2;
    const lateArrivalPenalty =
      settings.attendanceRules?.lateArrivalPenalty || 50;
    const absencePenalty = settings.attendanceRules?.absencePenalty || 100;

    let automaticPenalties = 0;
    if (lateDays >= lateDaysThreshold) {
      automaticPenalties += lateArrivalPenalty;
    }
    if (absentDays >= absentDaysThreshold) {
      automaticPenalties += absencePenalty;
    }

    return (
      employee.salary -
      employeePenalties -
      employeeAdvances +
      overtimeBonus -
      automaticPenalties
    );
  };

  const calculateCurrentSalary = (employeeId: number) => {
    return calculateCurrentSalaryForEmployee(employeeId);
  };

  const handleManualReset = () => {
    if (
      confirm(
        "Are you sure you want to reset all payroll data for the current month? This will reset all salaries to base amounts and clear advances/penalties.",
      )
    ) {
      dataManager.resetMonthlyPayroll();
      loadPayrollData();
    }
  };

  const isFirstDayOfMonth = () => {
    return new Date().getDate() === 1;
  };

  // Auto-reset on first day of month
  useEffect(() => {
    if (isFirstDayOfMonth()) {
      const lastReset = localStorage.getItem("salon_last_payroll_reset");
      const currentMonth = new Date().toISOString().slice(0, 7);

      if (lastReset !== currentMonth) {
        dataManager.resetMonthlyPayroll();
        localStorage.setItem("salon_last_payroll_reset", currentMonth);
        loadPayrollData();
      }
    }
  }, []);

  const togglePayrollStatus = (employeeId: number) => {
    const payrollRecord = dataManager.getPayrollByEmployee(employeeId);
    if (payrollRecord) {
      const newStatus = payrollRecord.status === "Paid" ? "Pending" : "Paid";
      dataManager.updatePayrollRecord(employeeId, { status: newStatus });
      loadPayrollData();
    }
  };

  const currentMonth = new Date().toLocaleDateString("en-US", {
    month: "long",
    year: "numeric",
  });
  const totalPayroll = employees.reduce(
    (sum, emp) => sum + calculateCurrentSalary(emp.id),
    0,
  );
  const totalAdvances = advances
    .filter(
      (a) =>
        a.month === new Date().toISOString().slice(0, 7) &&
        a.status === "active",
    )
    .reduce((sum, a) => sum + a.amount, 0);
  const totalPenalties = penalties
    .filter((p) => p.date.startsWith(new Date().toISOString().slice(0, 7)))
    .reduce((sum, p) => sum + p.amount, 0);

  return (
    <div className="space-y-6 p-6 bg-background">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">
          Payroll Management
        </h2>
        <div className="flex space-x-2">
          <Button variant="outline">
            <BarChart3 className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Button onClick={handleManualReset} variant="destructive">
            <DollarSign className="mr-2 h-4 w-4" />
            Reset Monthly Payroll
          </Button>
        </div>
      </div>

      {/* Payroll Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Payroll</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${totalPayroll.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Current month total</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Advances
            </CardTitle>
            <DollarSign className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              -${totalAdvances.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Deducted from salaries
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Penalties
            </CardTitle>
            <DollarSign className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              -${totalPenalties.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Late/absence penalties
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Employees
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {employees.filter((e) => e.status === "Active").length}
            </div>
            <p className="text-xs text-muted-foreground">Receiving salary</p>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Reset Info */}
      <Card>
        <CardHeader>
          <CardTitle>Payroll Information</CardTitle>
          <CardDescription>
            Current payroll period: {currentMonth}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h4 className="font-medium">Auto-Reset Schedule</h4>
              <p className="text-sm text-muted-foreground">
                Payroll automatically resets to base salaries on the 1st of each
                month.
                {isFirstDayOfMonth() && (
                  <span className="text-green-600 font-medium">
                    {" "}
                    (Today is reset day!)
                  </span>
                )}
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Manual Reset</h4>
              <p className="text-sm text-muted-foreground">
                Use the "Reset Monthly Payroll" button to manually reset all
                salaries to base amounts and clear current month adjustments.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Employee Payroll Table */}
      <Card>
        <CardHeader>
          <CardTitle>Employee Payroll Details</CardTitle>
          <CardDescription>
            Current month salary breakdown for all employees
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employee</TableHead>
                <TableHead>Base Salary</TableHead>
                <TableHead>Advances</TableHead>
                <TableHead>Penalties</TableHead>
                <TableHead>Overtime Bonus</TableHead>
                <TableHead>Current Salary</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {employees.map((employee) => {
                const currentMonth = new Date().toISOString().slice(0, 7);
                const employeePenalties = penalties
                  .filter(
                    (p) =>
                      p.employee_id === employee.id &&
                      p.date.startsWith(currentMonth),
                  )
                  .reduce((sum, p) => sum + p.amount, 0);
                const employeeAdvances = advances
                  .filter(
                    (a) =>
                      a.employee_id === employee.id &&
                      a.month === currentMonth &&
                      a.status === "active",
                  )
                  .reduce((sum, a) => sum + a.amount, 0);
                const monthlyAttendance = attendanceRecords.filter(
                  (r) =>
                    r.employee_id === employee.id &&
                    r.date.startsWith(currentMonth),
                );
                const totalOvertime = monthlyAttendance.reduce(
                  (sum, r) => sum + (r.overtime || 0),
                  0,
                );
                const overtimeBonus =
                  totalOvertime *
                  (settings.attendanceRules?.overtimeReward || 25);

                // Calculate automatic penalties based on attendance rules
                const lateDays = monthlyAttendance.filter(
                  (r) => r.status === "Late",
                ).length;
                const absentDays = monthlyAttendance.filter(
                  (r) => r.status === "Absent",
                ).length;
                const lateDaysThreshold =
                  settings.attendanceRules?.lateDaysThreshold || 5;
                const absentDaysThreshold =
                  settings.attendanceRules?.absentDaysThreshold || 2;
                const lateArrivalPenalty =
                  settings.attendanceRules?.lateArrivalPenalty || 50;
                const absencePenalty =
                  settings.attendanceRules?.absencePenalty || 100;

                let automaticPenalties = 0;
                if (lateDays >= lateDaysThreshold) {
                  automaticPenalties += lateArrivalPenalty;
                }
                if (absentDays >= absentDaysThreshold) {
                  automaticPenalties += absencePenalty;
                }

                const currentSalary = calculateCurrentSalary(employee.id);

                return (
                  <TableRow key={employee.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                          <span className="text-sm font-medium">
                            {employee.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium">{employee.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {employee.position}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      ${employee.salary.toLocaleString()}
                    </TableCell>
                    <TableCell
                      className={
                        employeeAdvances > 0
                          ? "text-red-600 font-medium"
                          : "text-muted-foreground"
                      }
                    >
                      {employeeAdvances > 0
                        ? `-${employeeAdvances.toLocaleString()}`
                        : "$0"}
                    </TableCell>
                    <TableCell
                      className={
                        employeePenalties + automaticPenalties > 0
                          ? "text-red-600 font-medium"
                          : "text-muted-foreground"
                      }
                    >
                      {employeePenalties + automaticPenalties > 0
                        ? `-${(employeePenalties + automaticPenalties).toLocaleString()}`
                        : "$0"}
                      {automaticPenalties > 0 && (
                        <div className="text-xs text-muted-foreground">
                          Auto: ${automaticPenalties} (
                          {lateDays >= lateDaysThreshold
                            ? `${lateDays} late days`
                            : ""}
                          {lateDays >= lateDaysThreshold &&
                          absentDays >= absentDaysThreshold
                            ? ", "
                            : ""}
                          {absentDays >= absentDaysThreshold
                            ? `${absentDays} absent days`
                            : ""}
                          )
                        </div>
                      )}
                    </TableCell>
                    <TableCell
                      className={
                        overtimeBonus > 0
                          ? "text-green-600 font-medium"
                          : "text-muted-foreground"
                      }
                    >
                      {overtimeBonus > 0
                        ? `+${overtimeBonus.toLocaleString()}`
                        : "$0"}
                    </TableCell>
                    <TableCell className="font-bold text-lg">
                      ${currentSalary.toLocaleString()}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          dataManager.getPayrollByEmployee(employee.id)
                            ?.status === "Paid"
                            ? "default"
                            : "secondary"
                        }
                        className="cursor-pointer hover:opacity-80"
                        onClick={() => togglePayrollStatus(employee.id)}
                      >
                        {dataManager.getPayrollByEmployee(employee.id)
                          ?.status || "Pending"}
                      </Badge>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Payroll History */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Payroll Adjustments</CardTitle>
          <CardDescription>
            Latest advances, penalties, and bonuses affecting current payroll
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="advances">
            <TabsList>
              <TabsTrigger value="advances">Recent Advances</TabsTrigger>
              <TabsTrigger value="penalties">Recent Penalties</TabsTrigger>
              <TabsTrigger value="overtime">
                Recent Overtime Bonuses
              </TabsTrigger>
            </TabsList>
            <TabsContent value="advances" className="space-y-4">
              <div className="space-y-2">
                {advances
                  .filter((a) => a.status === "active")
                  .sort(
                    (a, b) =>
                      new Date(b.created_at).getTime() -
                      new Date(a.created_at).getTime(),
                  )
                  .slice(0, 10)
                  .map((advance) => (
                    <div
                      key={advance.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="h-6 w-6 rounded-full bg-red-100 flex items-center justify-center">
                          <DollarSign className="h-3 w-3 text-red-600" />
                        </div>
                        <div>
                          <div className="font-medium">
                            {advance.employee_name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {advance.reason}
                          </div>
                        </div>
                      </div>
                      <div className="text-right flex items-center space-x-2">
                        <div>
                          <div className="font-medium text-red-600">
                            -${advance.amount.toLocaleString()}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(advance.date).toLocaleDateString()}
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (
                              confirm(
                                "Are you sure you want to delete this advance?",
                              )
                            ) {
                              const success = dataManager.deleteAdvance(
                                advance.id,
                              );
                              if (success) {
                                loadPayrollData();
                              } else {
                                alert("Failed to delete advance");
                              }
                            }
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                {advances.filter((a) => a.status === "active").length === 0 && (
                  <p className="text-center py-8 text-muted-foreground">
                    No recent advances
                  </p>
                )}
              </div>
            </TabsContent>
            <TabsContent value="penalties" className="space-y-4">
              <div className="space-y-2">
                {penalties
                  .filter((p) =>
                    p.date.startsWith(new Date().toISOString().slice(0, 7)),
                  )
                  .sort(
                    (a, b) =>
                      new Date(b.created_at).getTime() -
                      new Date(a.created_at).getTime(),
                  )
                  .slice(0, 10)
                  .map((penalty) => (
                    <div
                      key={penalty.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="h-6 w-6 rounded-full bg-red-100 flex items-center justify-center">
                          <UserCheck className="h-3 w-3 text-red-600" />
                        </div>
                        <div>
                          <div className="font-medium">
                            {penalty.employee_name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {penalty.reason}
                          </div>
                        </div>
                      </div>
                      <div className="text-right flex items-center space-x-2">
                        <div>
                          <div className="font-medium text-red-600">
                            -${penalty.amount.toLocaleString()}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(penalty.date).toLocaleDateString()}
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (
                              confirm(
                                "Are you sure you want to delete this penalty?",
                              )
                            ) {
                              const success = dataManager.deletePenalty(
                                penalty.id,
                              );
                              if (success) {
                                loadPayrollData();
                              } else {
                                alert("Failed to delete penalty");
                              }
                            }
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                {penalties.length === 0 && (
                  <p className="text-center py-8 text-muted-foreground">
                    No recent penalties
                  </p>
                )}
              </div>
            </TabsContent>
            <TabsContent value="overtime" className="space-y-4">
              <div className="space-y-2">
                {overtimeBonuses
                  .sort(
                    (a, b) =>
                      new Date(b.created_at).getTime() -
                      new Date(a.created_at).getTime(),
                  )
                  .slice(0, 10)
                  .map((bonus) => (
                    <div
                      key={bonus.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="h-6 w-6 rounded-full bg-green-100 flex items-center justify-center">
                          <Clock className="h-3 w-3 text-green-600" />
                        </div>
                        <div>
                          <div className="font-medium">
                            {bonus.employee_name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {bonus.overtime_hours.toFixed(1)} hours overtime
                          </div>
                        </div>
                      </div>
                      <div className="text-right flex items-center space-x-2">
                        <div>
                          <div className="font-medium text-green-600">
                            +${bonus.bonus_amount.toLocaleString()}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {bonus.month}
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (
                              confirm(
                                "Are you sure you want to remove this overtime bonus? This will reset the overtime hours for this employee.",
                              )
                            ) {
                              const success = dataManager.resetEmployeeOvertime(
                                bonus.employee_id,
                                bonus.month,
                              );
                              if (success) {
                                loadPayrollData();
                              } else {
                                alert("Failed to reset overtime hours");
                              }
                            }
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                {overtimeBonuses.length === 0 && (
                  <p className="text-center py-8 text-muted-foreground">
                    No overtime bonuses this month
                  </p>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

// Service Management component
const ServiceManagement = () => {
  const [services, setServices] = useState<any[]>([]);
  const [packages, setPackages] = useState<any[]>([]);
  const [dresses, setDresses] = useState<any[]>([]);
  const [showServiceDialog, setShowServiceDialog] = useState(false);
  const [showPackageDialog, setShowPackageDialog] = useState(false);
  const [showDressDialog, setShowDressDialog] = useState(false);
  const [editingService, setEditingService] = useState<any>(null);
  const [editingPackage, setEditingPackage] = useState<any>(null);
  const [editingDress, setEditingDress] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [colorFilter, setColorFilter] = useState<string[]>([]);
  const [availableColors] = useState([
    "Black",
    "White",
    "Red",
    "Blue",
    "Green",
    "Pink",
    "Purple",
    "Yellow",
    "Orange",
    "Brown",
    "Gray",
    "Navy",
    "Burgundy",
    "Ivory",
  ]);

  const [newService, setNewService] = useState({
    name: "",
    duration: 30,
    price: 0,
    description: "",
  });

  const [newPackage, setNewPackage] = useState({
    name: "",
    price: 0,
    description: "",
    serviceIds: [] as number[],
  });

  const [newDress, setNewDress] = useState({
    name: "",
    rental_price: 0,
    status: "Available" as "Available" | "Rented" | "Maintenance",
    colors: [] as string[],
    photos: [] as string[],
    description: "",
  });

  const dataManager = DatabaseManager.getInstance();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setServices(dataManager.getServices());
    setPackages(dataManager.getPackages());
    setDresses(dataManager.getDresses());
  };

  const handleAddService = () => {
    if (!newService.name || newService.price <= 0) {
      alert("Please fill in all required fields.");
      return;
    }

    try {
      if (editingService) {
        dataManager.updateService(editingService.id, newService);
      } else {
        dataManager.addService(newService);
      }
      loadData();
      setShowServiceDialog(false);
      setEditingService(null);
      setNewService({ name: "", duration: 30, price: 0, description: "" });
    } catch (error) {
      alert("Error saving service.");
    }
  };

  const handleEditService = (service: any) => {
    setEditingService(service);
    setNewService({
      name: service.name,
      duration: service.duration,
      price: service.price,
      description: service.description || "",
    });
    setShowServiceDialog(true);
  };

  const handleDeleteService = (id: number) => {
    if (confirm("Are you sure you want to delete this service?")) {
      dataManager.deleteService(id);
      loadData();
    }
  };

  const handleAddPackage = () => {
    if (!newPackage.name || newPackage.price <= 0) {
      alert("Please fill in all required fields.");
      return;
    }

    try {
      if (editingPackage) {
        dataManager.updatePackage(
          editingPackage.id,
          {
            name: newPackage.name,
            price: newPackage.price,
            description: newPackage.description,
          },
          newPackage.serviceIds,
        );
      } else {
        dataManager.addPackage(
          {
            name: newPackage.name,
            price: newPackage.price,
            description: newPackage.description,
          },
          newPackage.serviceIds,
        );
      }
      loadData();
      setShowPackageDialog(false);
      setEditingPackage(null);
      setNewPackage({ name: "", price: 0, description: "", serviceIds: [] });
    } catch (error) {
      alert("Error saving package.");
    }
  };

  const handleEditPackage = (pkg: any) => {
    const packageServices = dataManager.getPackageServices(pkg.id);
    setEditingPackage(pkg);
    setNewPackage({
      name: pkg.name,
      price: pkg.price,
      description: pkg.description || "",
      serviceIds: packageServices.map((s) => s.id),
    });
    setShowPackageDialog(true);
  };

  const handleDeletePackage = (id: number) => {
    if (confirm("Are you sure you want to delete this package?")) {
      dataManager.deletePackage(id);
      loadData();
    }
  };

  const handleAddDress = () => {
    if (!newDress.name || newDress.rental_price <= 0) {
      alert("Please fill in all required fields.");
      return;
    }

    try {
      const dressData = {
        ...newDress,
        colors: JSON.stringify(newDress.colors),
        photos: JSON.stringify(newDress.photos),
      };

      if (editingDress) {
        dataManager.updateDress(editingDress.id, dressData);
      } else {
        dataManager.addDress(dressData);
      }
      loadData();
      setShowDressDialog(false);
      setEditingDress(null);
      setNewDress({
        name: "",
        rental_price: 0,
        status: "Available",
        colors: [],
        photos: [],
        description: "",
      });
    } catch (error) {
      alert("Error saving dress.");
    }
  };

  const handleEditDress = (dress: any) => {
    setEditingDress(dress);
    setNewDress({
      name: dress.name,
      rental_price: dress.rental_price,
      status: dress.status,
      colors: JSON.parse(dress.colors),
      photos: JSON.parse(dress.photos),
      description: dress.description || "",
    });
    setShowDressDialog(true);
  };

  const handleDeleteDress = (id: number) => {
    if (confirm("Are you sure you want to delete this dress?")) {
      dataManager.deleteDress(id);
      loadData();
    }
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setNewDress((prev) => ({
          ...prev,
          photos: [...prev.photos, result],
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const removePhoto = (index: number) => {
    setNewDress((prev) => ({
      ...prev,
      photos: prev.photos.filter((_, i) => i !== index),
    }));
  };

  const filteredDresses = dataManager.searchDresses({
    status: statusFilter === "all" ? undefined : statusFilter,
    colors: colorFilter.length > 0 ? colorFilter : undefined,
    searchTerm: searchTerm || undefined,
  });

  return (
    <div className="space-y-6 p-6 bg-background">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">
          Service Management
        </h2>
      </div>

      <Tabs defaultValue="services">
        <TabsList>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="packages">Packages</TabsTrigger>
          <TabsTrigger value="dresses">Dress Rentals</TabsTrigger>
        </TabsList>

        <TabsContent value="services" className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Input
                placeholder="Search services..."
                className="max-w-sm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button onClick={() => setShowServiceDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Service
            </Button>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Service Name</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {services
                    .filter(
                      (service) =>
                        service.name
                          .toLowerCase()
                          .includes(searchTerm.toLowerCase()) ||
                        (service.description &&
                          service.description
                            .toLowerCase()
                            .includes(searchTerm.toLowerCase())),
                    )
                    .map((service) => (
                      <TableRow key={service.id}>
                        <TableCell className="font-medium">
                          {service.name}
                        </TableCell>
                        <TableCell>{service.duration} min</TableCell>
                        <TableCell>${service.price}</TableCell>
                        <TableCell className="max-w-xs truncate">
                          {service.description || "No description"}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditService(service)}
                            >
                              Edit
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteService(service.id)}
                            >
                              Delete
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="packages" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Service Packages</h3>
            <Button onClick={() => setShowPackageDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Package
            </Button>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {packages.map((pkg) => {
              const packageServices = dataManager.getPackageServices(pkg.id);
              return (
                <Card key={pkg.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>{pkg.name}</span>
                      <Badge variant="secondary">${pkg.price}</Badge>
                    </CardTitle>
                    <CardDescription>
                      {pkg.description || "No description"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">
                        Included Services:
                      </h4>
                      {packageServices.length > 0 ? (
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {packageServices.map((service) => (
                            <li
                              key={service.id}
                              className="flex justify-between"
                            >
                              <span>{service.name}</span>
                              <span>{service.duration}min</span>
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-sm text-muted-foreground">
                          No services included
                        </p>
                      )}
                    </div>
                    <div className="flex space-x-2 mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditPackage(pkg)}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeletePackage(pkg.id)}
                      >
                        Delete
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="dresses" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Dress Rentals</h3>
            <Button onClick={() => setShowDressDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Dress
            </Button>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-4">
            <Input
              placeholder="Search dresses..."
              className="max-w-sm"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Available">Available</SelectItem>
                <SelectItem value="Rented">Rented</SelectItem>
                <SelectItem value="Maintenance">Maintenance</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex items-center space-x-2">
              <Label>Colors:</Label>
              {availableColors.map((color) => (
                <div key={color} className="flex items-center space-x-1">
                  <input
                    type="checkbox"
                    id={`color-${color}`}
                    checked={colorFilter.includes(color)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setColorFilter([...colorFilter, color]);
                      } else {
                        setColorFilter(colorFilter.filter((c) => c !== color));
                      }
                    }}
                    className="rounded"
                  />
                  <Label htmlFor={`color-${color}`} className="text-xs">
                    {color}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredDresses.map((dress) => {
              const colors = JSON.parse(dress.colors);
              const photos = JSON.parse(dress.photos);
              return (
                <Card key={dress.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="truncate">{dress.name}</span>
                      <Badge
                        variant={
                          dress.status === "Available"
                            ? "default"
                            : dress.status === "Rented"
                              ? "destructive"
                              : "secondary"
                        }
                      >
                        {dress.status}
                      </Badge>
                    </CardTitle>
                    <CardDescription>
                      ${dress.rental_price} rental price
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {photos.length > 0 && (
                      <div className="mb-4">
                        <img
                          src={photos[0]}
                          alt={dress.name}
                          className="w-full h-32 object-cover rounded-md"
                        />
                      </div>
                    )}
                    <div className="space-y-2">
                      <div>
                        <h4 className="text-sm font-medium">
                          Available Colors:
                        </h4>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {colors.map((color: string) => (
                            <Badge
                              key={color}
                              variant="outline"
                              className="text-xs"
                            >
                              {color}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      {dress.description && (
                        <p className="text-sm text-muted-foreground">
                          {dress.description}
                        </p>
                      )}
                    </div>
                    <div className="flex space-x-2 mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditDress(dress)}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteDress(dress.id)}
                      >
                        Delete
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
      </Tabs>

      {/* Service Dialog */}
      <Dialog open={showServiceDialog} onOpenChange={setShowServiceDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingService ? "Edit Service" : "Add New Service"}
            </DialogTitle>
            <DialogDescription>Fill in the service details</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="service-name">Service Name *</Label>
              <Input
                id="service-name"
                value={newService.name}
                onChange={(e) =>
                  setNewService({ ...newService, name: e.target.value })
                }
                placeholder="Enter service name"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="service-duration">Duration (minutes) *</Label>
                <Input
                  id="service-duration"
                  type="number"
                  min="1"
                  value={newService.duration}
                  onChange={(e) =>
                    setNewService({
                      ...newService,
                      duration: parseInt(e.target.value) || 0,
                    })
                  }
                  placeholder="30"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="service-price">Price ($) *</Label>
                <Input
                  id="service-price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={newService.price}
                  onChange={(e) =>
                    setNewService({
                      ...newService,
                      price: parseFloat(e.target.value) || 0,
                    })
                  }
                  placeholder="0.00"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="service-description">Description</Label>
              <Textarea
                id="service-description"
                value={newService.description}
                onChange={(e) =>
                  setNewService({ ...newService, description: e.target.value })
                }
                placeholder="Enter service description"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowServiceDialog(false);
                setEditingService(null);
                setNewService({
                  name: "",
                  duration: 30,
                  price: 0,
                  description: "",
                });
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleAddService}>
              {editingService ? "Update" : "Add"} Service
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Package Dialog */}
      <Dialog open={showPackageDialog} onOpenChange={setShowPackageDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingPackage ? "Edit Package" : "Add New Package"}
            </DialogTitle>
            <DialogDescription>
              Create a package with multiple services
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="package-name">Package Name *</Label>
              <Input
                id="package-name"
                value={newPackage.name}
                onChange={(e) =>
                  setNewPackage({ ...newPackage, name: e.target.value })
                }
                placeholder="Enter package name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="package-price">Package Price ($) *</Label>
              <Input
                id="package-price"
                type="number"
                min="0"
                step="0.01"
                value={newPackage.price}
                onChange={(e) =>
                  setNewPackage({
                    ...newPackage,
                    price: parseFloat(e.target.value) || 0,
                  })
                }
                placeholder="0.00"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="package-description">Description</Label>
              <Textarea
                id="package-description"
                value={newPackage.description}
                onChange={(e) =>
                  setNewPackage({ ...newPackage, description: e.target.value })
                }
                placeholder="Enter package description"
              />
            </div>
            <div className="space-y-2">
              <Label>Included Services (optional)</Label>
              <div className="max-h-48 overflow-y-auto space-y-2 border rounded-md p-3">
                {services.map((service) => (
                  <div key={service.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`service-${service.id}`}
                      checked={newPackage.serviceIds.includes(service.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setNewPackage({
                            ...newPackage,
                            serviceIds: [...newPackage.serviceIds, service.id],
                          });
                        } else {
                          setNewPackage({
                            ...newPackage,
                            serviceIds: newPackage.serviceIds.filter(
                              (id) => id !== service.id,
                            ),
                          });
                        }
                      }}
                      className="rounded"
                    />
                    <Label htmlFor={`service-${service.id}`} className="flex-1">
                      <div className="flex justify-between">
                        <span>{service.name}</span>
                        <span className="text-muted-foreground">
                          {service.duration}min - ${service.price}
                        </span>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowPackageDialog(false);
                setEditingPackage(null);
                setNewPackage({
                  name: "",
                  price: 0,
                  description: "",
                  serviceIds: [],
                });
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleAddPackage}>
              {editingPackage ? "Update" : "Add"} Package
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dress Dialog */}
      <Dialog open={showDressDialog} onOpenChange={setShowDressDialog}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingDress ? "Edit Dress" : "Add New Dress"}
            </DialogTitle>
            <DialogDescription>
              Add dress details including photos and colors
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="dress-name">Dress Name *</Label>
              <Input
                id="dress-name"
                value={newDress.name}
                onChange={(e) =>
                  setNewDress({ ...newDress, name: e.target.value })
                }
                placeholder="Enter dress name"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dress-price">Rental Price ($) *</Label>
                <Input
                  id="dress-price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={newDress.rental_price}
                  onChange={(e) =>
                    setNewDress({
                      ...newDress,
                      rental_price: parseFloat(e.target.value) || 0,
                    })
                  }
                  placeholder="0.00"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="dress-status">Status</Label>
                <Select
                  value={newDress.status}
                  onValueChange={(
                    value: "Available" | "Rented" | "Maintenance",
                  ) => setNewDress({ ...newDress, status: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Available">Available</SelectItem>
                    <SelectItem value="Rented">Rented</SelectItem>
                    <SelectItem value="Maintenance">Maintenance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label>Available Colors</Label>
              <div className="grid grid-cols-4 gap-2">
                {availableColors.map((color) => (
                  <div key={color} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`dress-color-${color}`}
                      checked={newDress.colors.includes(color)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setNewDress({
                            ...newDress,
                            colors: [...newDress.colors, color],
                          });
                        } else {
                          setNewDress({
                            ...newDress,
                            colors: newDress.colors.filter((c) => c !== color),
                          });
                        }
                      }}
                      className="rounded"
                    />
                    <Label htmlFor={`dress-color-${color}`} className="text-sm">
                      {color}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
            <div className="space-y-2">
              <Label>Photos</Label>
              <div className="space-y-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() =>
                    document.getElementById("photo-upload")?.click()
                  }
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Photo
                </Button>
                <input
                  id="photo-upload"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handlePhotoUpload}
                />
                {newDress.photos.length > 0 && (
                  <div className="grid grid-cols-3 gap-2">
                    {newDress.photos.map((photo, index) => (
                      <div key={index} className="relative">
                        <img
                          src={photo}
                          alt={`Dress photo ${index + 1}`}
                          className="w-full h-24 object-cover rounded-md"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-1 right-1 h-6 w-6 p-0"
                          onClick={() => removePhoto(index)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="dress-description">Description</Label>
              <Textarea
                id="dress-description"
                value={newDress.description}
                onChange={(e) =>
                  setNewDress({ ...newDress, description: e.target.value })
                }
                placeholder="Enter dress description"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowDressDialog(false);
                setEditingDress(null);
                setNewDress({
                  name: "",
                  rental_price: 0,
                  status: "Available",
                  colors: [],
                  photos: [],
                  description: "",
                });
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleAddDress}>
              {editingDress ? "Update" : "Add"} Dress
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Customer interface
interface Customer {
  id: number;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  notes?: string;
  instagram?: string;
  facebook?: string;
  twitter?: string;
  total_bookings: number;
  total_spent: number;
  last_visit?: string;
  favorite_services?: string[];
  created_at: string;
  updated_at: string;
}









    const customerData = {
      name: customerSearchTerm.trim(),
      phone: "",
      email: "",
      address: "",
      notes: "",
      instagram: "",
      facebook: "",
      twitter: "",
      total_bookings: 0,
      total_spent: 0,
      favorite_services: JSON.stringify([]),
    };

    const newCustomer = dataManager.addCustomer(customerData);
    setCustomers((prev) => [...prev, newCustomer]);
    handleCustomerSelect(newCustomer);
  };

  const handleManualPriceToggle = () => {
    setManualPriceOverride(!manualPriceOverride);
    if (!manualPriceOverride) {
      setNewBooking((prev) => ({ ...prev, manual_total: prev.total_amount }));
    } else {
      calculateTotalAmount();
    }
  };

  const handleCreateBooking = () => {
    // Validation
    if (!newBooking.date) {
      alert("Please select a date for the booking.");
      return;
    }

    // Validate dress rental period if dresses are selected
    if (newBooking.dress_ids.length > 0) {
      if (
        !newBooking.dress_rental_start_date ||
        !newBooking.dress_rental_end_date
      ) {
        alert("Please specify the rental period for selected dresses.");
        return;
      }

      if (
        new Date(newBooking.dress_rental_start_date) >
        new Date(newBooking.dress_rental_end_date)
      ) {
        alert("Rental start date must be before end date.");
        return;
      }

      // Check dress availability for the rental period
      const validation = dataManager.validateBookingDressAvailability(
        newBooking.dress_ids,
        newBooking.dress_rental_start_date,
        newBooking.dress_rental_end_date,
      );

      if (!validation.valid) {
        const conflictNames = validation.conflicts
          .map((c) => c.dressName)
          .join(", ");
        alert(
          `The following dresses are not available for the selected rental period: ${conflictNames}`,
        );
        return;
      }
    }

    const booking: Booking = {
      id: Math.max(0, ...bookings.map((b) => b.id)) + 1,
      title: newBooking.title || "New Booking",
      notes: newBooking.notes,
      date: newBooking.date,
      customer_id: newBooking.customer_id,
      customer_name: newBooking.customer_name,
      employee_ids: newBooking.employee_ids,
      start_time: newBooking.start_time,
      end_time: newBooking.end_time,
      service_ids: newBooking.service_ids,
      package_ids: newBooking.package_ids,
      dress_ids: newBooking.dress_ids,
      total_amount: manualPriceOverride
        ? newBooking.manual_total
        : newBooking.total_amount,
      deposit: newBooking.deposit,
      discount_percentage: newBooking.discount_percentage,
      status: "Confirmed",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const bookingData = {
      ...booking,
      employee_ids: JSON.stringify(booking.employee_ids),
      service_ids: JSON.stringify(booking.service_ids),
      package_ids: JSON.stringify(booking.package_ids),
      dress_ids: JSON.stringify(booking.dress_ids),
    };

    const savedBooking = dataManager.addBooking(bookingData);
    const updatedBookings = [
      ...bookings,
      {
        ...savedBooking,
        service_ids: JSON.parse(savedBooking.service_ids || "[]"),
        package_ids: JSON.parse(savedBooking.package_ids || "[]"),
        dress_ids: JSON.parse(savedBooking.dress_ids || "[]"),
        employee_ids: JSON.parse(savedBooking.employee_ids || "[]"),
      },
    ];
    setBookings(updatedBookings);

    alert("Booking created successfully!");

    // Reset form
    setNewBooking({
      title: "",
      notes: "",
      date: date
        ? new Date(date.getTime() - date.getTimezoneOffset() * 60000)
            .toISOString()
            .split("T")[0]
        : "",
      customer_id: null,
      customer_name: "",
      employee_ids: [],
      start_time: "",
      end_time: "",
      service_ids: [],
      package_ids: [],
      dress_ids: [],
      dress_rental_start_date: "",
      dress_rental_end_date: "",
      total_amount: 0,
      manual_total: 0,
      deposit: 0,
      discount_percentage: 0,
    });
    setCustomerSearchTerm("");
    setManualPriceOverride(false);
    setShowAddCustomer(false);
    setShowNewBookingDialog(false);
  };

  const handleEditBooking = (booking: Booking) => {
    setEditingBooking(booking);

    // Get the booking from database to access rental dates
    const dbBooking = dataManager.getBookingById(booking.id);

    setNewBooking({
      title: booking.title,
      notes: booking.notes,
      date: booking.date,
      customer_id: booking.customer_id,
      customer_name: booking.customer_name,
      employee_ids: booking.employee_ids,
      start_time: booking.start_time,
      end_time: booking.end_time,
      service_ids: booking.service_ids,
      package_ids: booking.package_ids,
      dress_ids: booking.dress_ids,
      dress_rental_start_date: dbBooking?.dress_rental_start_date || "",
      dress_rental_end_date: dbBooking?.dress_rental_end_date || "",
      total_amount: booking.total_amount,
      manual_total: booking.total_amount,
      deposit: booking.deposit,
      discount_percentage: booking.discount_percentage,
    });
    setCustomerSearchTerm(booking.customer_name);
    setShowNewBookingDialog(true);
  };

  const handleUpdateBooking = () => {
    if (!editingBooking) return;

    // Validate dress rental period if dresses are selected
    if (newBooking.dress_ids.length > 0) {
      if (
        !newBooking.dress_rental_start_date ||
        !newBooking.dress_rental_end_date
      ) {
        alert("Please specify the rental period for selected dresses.");
        return;
      }

      if (
        new Date(newBooking.dress_rental_start_date) >
        new Date(newBooking.dress_rental_end_date)
      ) {
        alert("Rental start date must be before end date.");
        return;
      }

      // Check dress availability for the rental period (excluding current booking)
      const validation = dataManager.validateBookingDressAvailability(
        newBooking.dress_ids,
        newBooking.dress_rental_start_date,
        newBooking.dress_rental_end_date,
        editingBooking.id,
      );

      if (!validation.valid) {
        const conflictNames = validation.conflicts
          .map((c) => c.dressName)
          .join(", ");
        alert(
          `The following dresses are not available for the selected rental period: ${conflictNames}`,
        );
        return;
      }
    }

    const updatedBooking: Booking = {
      ...editingBooking,
      title: newBooking.title || "Updated Booking",
      notes: newBooking.notes,
      date: newBooking.date,
      customer_id: newBooking.customer_id,
      customer_name: newBooking.customer_name,
      employee_ids: newBooking.employee_ids,
      start_time: newBooking.start_time,
      end_time: newBooking.end_time,
      service_ids: newBooking.service_ids,
      package_ids: newBooking.package_ids,
      dress_ids: newBooking.dress_ids,
      total_amount: manualPriceOverride
        ? newBooking.manual_total
        : newBooking.total_amount,
      deposit: newBooking.deposit,
      discount_percentage: newBooking.discount_percentage,
      updated_at: new Date().toISOString(),
    };

    const bookingData = {
      ...updatedBooking,
      employee_ids: JSON.stringify(updatedBooking.employee_ids),
      service_ids: JSON.stringify(updatedBooking.service_ids),
      package_ids: JSON.stringify(updatedBooking.package_ids),
      dress_ids: JSON.stringify(updatedBooking.dress_ids),
      dress_rental_start_date:
        newBooking.dress_ids.length > 0
          ? newBooking.dress_rental_start_date
          : null,
      dress_rental_end_date:
        newBooking.dress_ids.length > 0
          ? newBooking.dress_rental_end_date
          : null,
    };

    dataManager.updateBooking(editingBooking.id, bookingData);
    const updatedBookings = bookings.map((b) =>
      b.id === editingBooking.id ? updatedBooking : b,
    );
    setBookings(updatedBookings);

    alert("Booking updated successfully!");
    setEditingBooking(null);
    setShowNewBookingDialog(false);
  };

  const handleCancelBooking = (bookingId: number) => {
    if (confirm("Are you sure you want to cancel this booking?")) {
      dataManager.updateBooking(bookingId, { status: "Cancelled" });
      const updatedBookings = bookings.map((b) =>
        b.id === bookingId
          ? {
              ...b,
              status: "Cancelled" as const,
              updated_at: new Date().toISOString(),
            }
          : b,
      );
      setBookings(updatedBookings);
    }
  };

  const handleStatusChange = (
    bookingId: number,
    newStatus: Booking["status"],
  ) => {
    dataManager.updateBooking(bookingId, { status: newStatus });
    const updatedBookings = bookings.map((b) =>
      b.id === bookingId
        ? { ...b, status: newStatus, updated_at: new Date().toISOString() }
        : b,
    );
    setBookings(updatedBookings);
  };

  const handleBookingClick = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowBookingDetails(true);
  };

  const getStatusColor = (status: Booking["status"]) => {
    switch (status) {
      case "Confirmed":
        return "default";
      case "In Progress":
        return "secondary";
      case "Cancelled":
        return "destructive";
      case "Completed":
        return "outline";
      default:
        return "default";
    }
  };

  const getNextStatus = (
    currentStatus: Booking["status"],
  ): Booking["status"] => {
    switch (currentStatus) {
      case "Confirmed":
        return "In Progress";
      case "In Progress":
        return "Completed";
      case "Completed":
        return "Confirmed";
      case "Cancelled":
        return "Confirmed";
      default:
        return "Confirmed";
    }
  };

  const filteredCustomers = customers.filter((customer) =>
    customer.name.toLowerCase().includes(customerSearchTerm.toLowerCase()),
  );

  const finalAmount = manualPriceOverride
    ? newBooking.manual_total
    : newBooking.total_amount;
  const amountAfterDeposit = Math.max(0, finalAmount - newBooking.deposit);

  // Filter bookings for selected date - fix timezone issue
  const selectedDateString = date
    ? new Date(date.getTime() - date.getTimezoneOffset() * 60000)
        .toISOString()
        .split("T")[0]
    : new Date().toISOString().split("T")[0];
  const dayBookings = bookings.filter(
    (booking) => booking.date === selectedDateString,
  );

  // Check if current time is between start and end time for "In Progress" status
  const isCurrentlyInProgress = (booking: Booking) => {
    if (!booking.start_time || !booking.end_time) return false;
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    const [startHour, startMin] = booking.start_time.split(":").map(Number);
    const [endHour, endMin] = booking.end_time.split(":").map(Number);
    const startTime = startHour * 60 + startMin;
    const endTime = endHour * 60 + endMin;
    return (
      currentTime >= startTime &&
      currentTime <= endTime &&
      booking.date === new Date().toISOString().split("T")[0]
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">
          Booking Management
        </h2>
        <Button
          onClick={() => {
            // Reset form when opening new booking dialog
            setNewBooking({
              title: "",
              notes: "",
              date: date
                ? new Date(date.getTime() - date.getTimezoneOffset() * 60000)
                    .toISOString()
                    .split("T")[0]
                : "",
              customer_id: null,
              customer_name: "",
              employee_ids: [],
              start_time: "",
              end_time: "",
              service_ids: [],
              package_ids: [],
              dress_ids: [],
              dress_rental_start_date: "",
              dress_rental_end_date: "",
              total_amount: 0,
              manual_total: 0,
              deposit: 0,
              discount_percentage: 0,
            });
            setCustomerSearchTerm("");
            setManualPriceOverride(false);
            setShowAddCustomer(false);
            setEditingBooking(null);
            setShowNewBookingDialog(true);
          }}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          New Booking
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Calendar</CardTitle>
            <CardDescription>Select a date to view bookings</CardDescription>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={date}
              onSelect={setDate}
              className="rounded-md border"
            />
          </CardContent>
        </Card>

        <Card className="col-span-2">
          <CardHeader>
            <CardTitle>Bookings for {date?.toLocaleDateString()}</CardTitle>
            <CardDescription>
              Manage appointments and services ({dayBookings.length} bookings)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dayBookings.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <CalendarIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No bookings for this date</p>
                  <p className="text-sm">Click "New Booking" to create one</p>
                </div>
              ) : (
                dayBookings
                  .sort((a, b) => a.start_time.localeCompare(b.start_time))
                  .map((booking) => {
                    const employeeNames =
                      booking.employee_ids
                        .map(
                          (id) => employees.find((emp) => emp.id === id)?.name,
                        )
                        .filter(Boolean)
                        .join(", ") || "No employee assigned";

                    const serviceNames = booking.service_ids
                      .map((id) => services.find((s) => s.id === id)?.name)
                      .filter(Boolean);

                    const packageNames = booking.package_ids
                      .map((id) => packages.find((p) => p.id === id)?.name)
                      .filter(Boolean);

                    const allServices =
                      [...serviceNames, ...packageNames].join(", ") ||
                      booking.title ||
                      "No services selected";

                    // Auto-update status if currently in progress
                    const currentStatus =
                      isCurrentlyInProgress(booking) &&
                      booking.status === "Confirmed"
                        ? "In Progress"
                        : booking.status;

                    if (currentStatus !== booking.status) {
                      handleStatusChange(booking.id, "In Progress");
                    }

                    return (
                      <div
                        key={booking.id}
                        className="flex items-start space-x-4 p-3 rounded-lg border cursor-pointer hover:bg-muted/50 transition-colors"
                        onClick={() => handleBookingClick(booking)}
                      >
                        <div className="bg-primary/10 p-2 rounded-md">
                          <Clock className="h-5 w-5 text-primary" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">
                              {booking.start_time
                                ? `${booking.start_time}${booking.end_time ? ` - ${booking.end_time}` : ""}`
                                : "Time not set"}
                            </h4>
                            <Badge
                              variant={getStatusColor(currentStatus)}
                              className="cursor-pointer hover:opacity-80"
                              onClick={(e) => {
                                e.stopPropagation();
                                if (currentStatus !== "Cancelled") {
                                  handleStatusChange(
                                    booking.id,
                                    getNextStatus(currentStatus),
                                  );
                                }
                              }}
                            >
                              {currentStatus}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {allServices} -{" "}
                            {booking.customer_name || "No customer"}
                          </p>
                          <div className="mt-2 flex items-center text-xs text-muted-foreground">
                            <span className="mr-2">Staff: {employeeNames}</span>
                            <span>Amount: ${booking.total_amount}</span>
                          </div>
                          {booking.notes && (
                            <p className="text-xs text-muted-foreground mt-1 italic">
                              {booking.notes}
                            </p>
                          )}
                        </div>
                        <div className="flex flex-col space-y-2">
                          {currentStatus === "Confirmed" && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStatusChange(booking.id, "Completed");
                              }}
                            >
                              Mark Completed
                            </Button>
                          )}
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditBooking(booking);
                              }}
                            >
                              Edit
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCancelBooking(booking.id);
                              }}
                              disabled={booking.status === "Cancelled"}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      </div>
                    );
                  })
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* New Booking Dialog */}
      <Dialog
        open={showNewBookingDialog}
        onOpenChange={setShowNewBookingDialog}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingBooking ? "Edit Booking" : "Create New Booking"}
            </DialogTitle>
            <DialogDescription>
              {editingBooking
                ? "Update the booking details"
                : "Fill in the booking details for the selected date"}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            {/* Basic Information */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="booking-title">Title (Optional)</Label>
                <Input
                  id="booking-title"
                  value={newBooking.title}
                  onChange={(e) =>
                    setNewBooking((prev) => ({
                      ...prev,
                      title: e.target.value,
                    }))
                  }
                  placeholder="Enter booking title"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="booking-date">Date *</Label>
                <Input
                  id="booking-date"
                  type="date"
                  value={newBooking.date}
                  onChange={(e) =>
                    setNewBooking((prev) => ({ ...prev, date: e.target.value }))
                  }
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="booking-notes">Notes (Optional)</Label>
              <Textarea
                id="booking-notes"
                value={newBooking.notes}
                onChange={(e) =>
                  setNewBooking((prev) => ({ ...prev, notes: e.target.value }))
                }
                placeholder="Enter any additional notes"
                rows={3}
              />
            </div>

            {/* Customer Selection */}
            <div className="space-y-2">
              <Label>Customer (Optional)</Label>
              <div className="space-y-2">
                <Input
                  placeholder="Search for existing customer or enter new name"
                  value={customerSearchTerm}
                  onChange={(e) => {
                    setCustomerSearchTerm(e.target.value);
                    setShowAddCustomer(
                      e.target.value.length > 0 &&
                        !filteredCustomers.some(
                          (c) =>
                            c.name.toLowerCase() ===
                            e.target.value.toLowerCase(),
                        ),
                    );
                  }}
                />
                {customerSearchTerm && (
                  <div className="border rounded-md max-h-32 overflow-y-auto">
                    {filteredCustomers.map((customer) => (
                      <div
                        key={customer.id}
                        className="p-2 hover:bg-muted cursor-pointer border-b last:border-b-0"
                        onClick={() => handleCustomerSelect(customer)}
                      >
                        <div className="font-medium">{customer.name}</div>
                        {customer.phone && (
                          <div className="text-sm text-muted-foreground">
                            {customer.phone}
                          </div>
                        )}
                      </div>
                    ))}
                    {showAddCustomer && (
                      <div
                        className="p-2 hover:bg-muted cursor-pointer border-b-0 bg-primary/5"
                        onClick={handleAddNewCustomer}
                      >
                        <div className="font-medium text-primary">
                          <Plus className="inline h-4 w-4 mr-1" />
                          Add "{customerSearchTerm}" as new customer
                        </div>
                      </div>
                    )}
                  </div>
                )}
                {newBooking.customer_name && (
                  <div className="p-2 bg-muted rounded-md">
                    <span className="text-sm">
                      Selected: <strong>{newBooking.customer_name}</strong>
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-2 h-6 w-6 p-0"
                      onClick={() => {
                        setNewBooking((prev) => ({
                          ...prev,
                          customer_id: null,
                          customer_name: "",
                        }));
                        setCustomerSearchTerm("");
                      }}
                    >
                      ×
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Time Selection */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start-time">Start Time (Optional)</Label>
                <Input
                  id="start-time"
                  type="time"
                  value={newBooking.start_time}
                  onChange={(e) =>
                    setNewBooking((prev) => ({
                      ...prev,
                      start_time: e.target.value,
                    }))
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="end-time">End Time (Optional)</Label>
                <Input
                  id="end-time"
                  type="time"
                  value={newBooking.end_time}
                  onChange={(e) =>
                    setNewBooking((prev) => ({
                      ...prev,
                      end_time: e.target.value,
                    }))
                  }
                />
              </div>
            </div>

            {/* Employee Selection */}
            <div className="space-y-2">
              <Label>Employees (Optional)</Label>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border rounded-md p-2">
                {employees.map((employee) => (
                  <div
                    key={employee.id}
                    className="flex items-center space-x-2"
                  >
                    <input
                      type="checkbox"
                      id={`employee-${employee.id}`}
                      checked={newBooking.employee_ids.includes(employee.id)}
                      onChange={() => handleEmployeeToggle(employee.id)}
                      className="rounded"
                    />
                    <Label
                      htmlFor={`employee-${employee.id}`}
                      className="text-sm"
                    >
                      {employee.name}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Services Selection */}
            <div className="space-y-2">
              <Label>Services (Optional)</Label>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border rounded-md p-2">
                {services.map((service) => (
                  <div
                    key={service.id}
                    className="flex items-center justify-between space-x-2"
                  >
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`service-${service.id}`}
                        checked={newBooking.service_ids.includes(service.id)}
                        onChange={() => handleServiceToggle(service.id)}
                        className="rounded"
                      />
                      <Label
                        htmlFor={`service-${service.id}`}
                        className="text-sm"
                      >
                        {service.name}
                      </Label>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      ${service.price}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Packages Selection */}
            <div className="space-y-2">
              <Label>Packages (Optional)</Label>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border rounded-md p-2">
                {packages.map((pkg) => (
                  <div
                    key={pkg.id}
                    className="flex items-center justify-between space-x-2"
                  >
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`package-${pkg.id}`}
                        checked={newBooking.package_ids.includes(pkg.id)}
                        onChange={() => handlePackageToggle(pkg.id)}
                        className="rounded"
                      />
                      <Label htmlFor={`package-${pkg.id}`} className="text-sm">
                        {pkg.name}
                      </Label>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      ${pkg.price}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Dresses Selection */}
            <div className="space-y-2">
              <Label>Available Dresses (Optional)</Label>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border rounded-md p-2">
                {dresses.map((dress) => (
                  <div
                    key={dress.id}
                    className="flex items-center justify-between space-x-2"
                  >
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`dress-${dress.id}`}
                        checked={newBooking.dress_ids.includes(dress.id)}
                        onChange={() => handleDressToggle(dress.id)}
                        className="rounded"
                      />
                      <Label htmlFor={`dress-${dress.id}`} className="text-sm">
                        {dress.name}
                      </Label>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      ${dress.rental_price}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Dress Rental Period - Only show if dresses are selected */}
            {newBooking.dress_ids.length > 0 && (
              <div className="space-y-2">
                <Label className="text-base font-medium">
                  Dress Rental Period *
                </Label>
                <p className="text-sm text-muted-foreground mb-3">
                  Specify the rental period for the selected dresses
                </p>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="rental-start-date">
                      Rental Start Date *
                    </Label>
                    <Input
                      id="rental-start-date"
                      type="date"
                      value={newBooking.dress_rental_start_date}
                      onChange={(e) =>
                        setNewBooking((prev) => ({
                          ...prev,
                          dress_rental_start_date: e.target.value,
                        }))
                      }
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="rental-end-date">Rental End Date *</Label>
                    <Input
                      id="rental-end-date"
                      type="date"
                      value={newBooking.dress_rental_end_date}
                      onChange={(e) =>
                        setNewBooking((prev) => ({
                          ...prev,
                          dress_rental_end_date: e.target.value,
                        }))
                      }
                      min={newBooking.dress_rental_start_date}
                      required
                    />
                  </div>
                </div>
                {newBooking.dress_rental_start_date &&
                  newBooking.dress_rental_end_date && (
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm text-blue-800">
                        📅 Rental Period:{" "}
                        {new Date(
                          newBooking.dress_rental_start_date,
                        ).toLocaleDateString()}{" "}
                        -{" "}
                        {new Date(
                          newBooking.dress_rental_end_date,
                        ).toLocaleDateString()}
                        (
                        {Math.ceil(
                          (new Date(
                            newBooking.dress_rental_end_date,
                          ).getTime() -
                            new Date(
                              newBooking.dress_rental_start_date,
                            ).getTime()) /
                            (1000 * 60 * 60 * 24),
                        ) + 1}{" "}
                        days)
                      </p>
                    </div>
                  )}
              </div>
            )}

            <Separator />

            {/* Pricing Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-lg font-medium">Pricing</Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={manualPriceOverride}
                    onCheckedChange={handleManualPriceToggle}
                  />
                  <Label className="text-sm">Manual Price Override</Label>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Discount Percentage</Label>
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={newBooking.discount_percentage}
                    onChange={(e) =>
                      setNewBooking((prev) => ({
                        ...prev,
                        discount_percentage: parseFloat(e.target.value) || 0,
                      }))
                    }
                    placeholder="0"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Deposit Amount</Label>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={newBooking.deposit}
                    onChange={(e) =>
                      setNewBooking((prev) => ({
                        ...prev,
                        deposit: parseFloat(e.target.value) || 0,
                      }))
                    }
                    placeholder="0.00"
                  />
                </div>
              </div>

              {manualPriceOverride && (
                <div className="space-y-2">
                  <Label>Manual Total Amount</Label>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={newBooking.manual_total}
                    onChange={(e) =>
                      setNewBooking((prev) => ({
                        ...prev,
                        manual_total: parseFloat(e.target.value) || 0,
                      }))
                    }
                    placeholder="0.00"
                  />
                </div>
              )}

              {/* Pricing Summary */}
              <div className="p-4 bg-muted rounded-lg space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal:</span>
                  <span>
                    $
                    {(
                      newBooking.service_ids.reduce(
                        (sum, id) =>
                          sum + (services.find((s) => s.id === id)?.price || 0),
                        0,
                      ) +
                      newBooking.package_ids.reduce(
                        (sum, id) =>
                          sum + (packages.find((p) => p.id === id)?.price || 0),
                        0,
                      ) +
                      newBooking.dress_ids.reduce(
                        (sum, id) =>
                          sum +
                          (dresses.find((d) => d.id === id)?.rental_price || 0),
                        0,
                      )
                    ).toFixed(2)}
                  </span>
                </div>
                {newBooking.discount_percentage > 0 && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Discount ({newBooking.discount_percentage}%):</span>
                    <span>
                      -$
                      {(
                        ((newBooking.service_ids.reduce(
                          (sum, id) =>
                            sum +
                            (services.find((s) => s.id === id)?.price || 0),
                          0,
                        ) +
                          newBooking.package_ids.reduce(
                            (sum, id) =>
                              sum +
                              (packages.find((p) => p.id === id)?.price || 0),
                            0,
                          ) +
                          newBooking.dress_ids.reduce(
                            (sum, id) =>
                              sum +
                              (dresses.find((d) => d.id === id)?.rental_price ||
                                0),
                            0,
                          )) *
                          newBooking.discount_percentage) /
                        100
                      ).toFixed(2)}
                    </span>
                  </div>
                )}
                <div className="flex justify-between font-medium">
                  <span>Total Amount:</span>
                  <span>${finalAmount.toFixed(2)}</span>
                </div>
                {newBooking.deposit > 0 && (
                  <div className="flex justify-between text-sm text-blue-600">
                    <span>Deposit Paid:</span>
                    <span>-${newBooking.deposit.toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>Amount Due:</span>
                  <span>${amountAfterDeposit.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowNewBookingDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={
                editingBooking ? handleUpdateBooking : handleCreateBooking
              }
            >
              {editingBooking ? "Update Booking" : "Create Booking"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Financial Dashboard component
const FinancialDashboard = () => {
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");
  const [financialData, setFinancialData] = useState({
    totalRevenue: 0,
    servicesRevenue: 0,
    packagesRevenue: 0,
    dressRentalsRevenue: 0,
    serviceCategories: [] as any[],
    topEmployees: [] as any[],
    periodRevenue: [] as any[],
  });

  const dataManager = DatabaseManager.getInstance();

  useEffect(() => {
    loadFinancialData();
  }, [selectedPeriod]);

  const loadFinancialData = () => {
    // Load bookings from database
    const bookings = dataManager.getBookings().map((booking) => ({
      ...booking,
      service_ids: JSON.parse(booking.service_ids || "[]"),
      package_ids: JSON.parse(booking.package_ids || "[]"),
      dress_ids: JSON.parse(booking.dress_ids || "[]"),
      employee_ids: JSON.parse(booking.employee_ids || "[]"),
    }));

    // Filter out cancelled bookings
    const validBookings = bookings.filter(
      (booking: any) => booking.status !== "Cancelled",
    );

    // Get services, packages, and dresses data
    const services = dataManager.getServices();
    const packages = dataManager.getPackages();
    const dresses = dataManager.getDresses();
    const employees = dataManager.getEmployees();

    // Calculate revenue by type
    let servicesRevenue = 0;
    let packagesRevenue = 0;
    let dressRentalsRevenue = 0;

    validBookings.forEach((booking: any) => {
      // Services revenue
      booking.service_ids?.forEach((serviceId: number) => {
        const service = services.find((s) => s.id === serviceId);
        if (service) {
          servicesRevenue += service.price;
        }
      });

      // Packages revenue
      booking.package_ids?.forEach((packageId: number) => {
        const pkg = packages.find((p) => p.id === packageId);
        if (pkg) {
          packagesRevenue += pkg.price;
        }
      });

      // Dress rentals revenue
      booking.dress_ids?.forEach((dressId: number) => {
        const dress = dresses.find((d) => d.id === dressId);
        if (dress) {
          dressRentalsRevenue += dress.rental_price;
        }
      });
    });

    const totalRevenue = validBookings.reduce(
      (sum: number, booking: any) => sum + booking.total_amount,
      0,
    );

    // Calculate service categories (simplified)
    const serviceCategories = [
      {
        category: "Individual Services",
        amount: servicesRevenue,
        percentage:
          totalRevenue > 0 ? (servicesRevenue / totalRevenue) * 100 : 0,
      },
      {
        category: "Service Packages",
        amount: packagesRevenue,
        percentage:
          totalRevenue > 0 ? (packagesRevenue / totalRevenue) * 100 : 0,
      },
      {
        category: "Dress Rentals",
        amount: dressRentalsRevenue,
        percentage:
          totalRevenue > 0 ? (dressRentalsRevenue / totalRevenue) * 100 : 0,
      },
    ].filter((cat) => cat.amount > 0);

    // Calculate employee performance
    const employeeStats: {
      [key: number]: { name: string; services: number; revenue: number };
    } = {};

    validBookings.forEach((booking: any) => {
      booking.employee_ids?.forEach((employeeId: number) => {
        const employee = employees.find((e) => e.id === employeeId);
        if (employee) {
          if (!employeeStats[employeeId]) {
            employeeStats[employeeId] = {
              name: employee.name,
              services: 0,
              revenue: 0,
            };
          }
          employeeStats[employeeId].services += 1;
          employeeStats[employeeId].revenue +=
            booking.total_amount / (booking.employee_ids?.length || 1);
        }
      });
    });

    const topEmployees = Object.values(employeeStats)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // Calculate period-based revenue
    const periodRevenue = calculatePeriodRevenue(validBookings, selectedPeriod);

    setFinancialData({
      totalRevenue,
      servicesRevenue,
      packagesRevenue,
      dressRentalsRevenue,
      serviceCategories,
      topEmployees,
      periodRevenue,
    });
  };

  const calculatePeriodRevenue = (bookings: any[], period: string) => {
    const now = new Date();
    const data: any[] = [];

    switch (period) {
      case "daily":
        // Last 7 days
        for (let i = 6; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(date.getDate() - i);
          const dateString = date.toISOString().split("T")[0];
          const dayRevenue = bookings
            .filter((b) => b.date === dateString)
            .reduce((sum, b) => sum + b.total_amount, 0);
          data.push({
            period: date.toLocaleDateString("en-US", { weekday: "short" }),
            revenue: dayRevenue,
          });
        }
        break;
      case "weekly":
        // Last 4 weeks
        for (let i = 3; i >= 0; i--) {
          const startDate = new Date(now);
          startDate.setDate(startDate.getDate() - i * 7 - 6);
          const endDate = new Date(now);
          endDate.setDate(endDate.getDate() - i * 7);

          const weekRevenue = bookings
            .filter((b) => {
              const bookingDate = new Date(b.date);
              return bookingDate >= startDate && bookingDate <= endDate;
            })
            .reduce((sum, b) => sum + b.total_amount, 0);
          data.push({ period: `Week ${4 - i}`, revenue: weekRevenue });
        }
        break;
      case "monthly":
        // Last 6 months
        for (let i = 5; i >= 0; i--) {
          const date = new Date(now);
          date.setMonth(date.getMonth() - i);
          const monthString = date.toISOString().slice(0, 7);
          const monthRevenue = bookings
            .filter((b) => b.date.startsWith(monthString))
            .reduce((sum, b) => sum + b.total_amount, 0);
          data.push({
            period: date.toLocaleDateString("en-US", { month: "short" }),
            revenue: monthRevenue,
          });
        }
        break;
      case "yearly":
        // Last 3 years
        for (let i = 2; i >= 0; i--) {
          const year = now.getFullYear() - i;
          const yearRevenue = bookings
            .filter((b) => b.date.startsWith(year.toString()))
            .reduce((sum, b) => sum + b.total_amount, 0);
          data.push({ period: year.toString(), revenue: yearRevenue });
        }
        break;
    }

    return data;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">
          Financial Dashboard
        </h2>
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={loadFinancialData}>
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${financialData.totalRevenue.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Excluding cancelled bookings
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Services Revenue
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${financialData.servicesRevenue.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Individual services only
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Packages Revenue
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${financialData.packagesRevenue.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Service packages</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Dress Rentals Revenue
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${financialData.dressRentalsRevenue.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Dress rental income</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Revenue by Category</CardTitle>
            <CardDescription>Breakdown of revenue sources</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {financialData.serviceCategories.length === 0 ? (
                <p className="text-center py-4 text-muted-foreground">
                  No revenue data available
                </p>
              ) : (
                financialData.serviceCategories.map((item, i) => (
                  <div key={i} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-3 h-3 rounded-full bg-primary"
                          style={{ opacity: 1 - i * 0.2 }}
                        />
                        <span>{item.category}</span>
                      </div>
                      <span className="text-sm">${item.amount.toFixed(2)}</span>
                    </div>
                    <Progress value={item.percentage} className="h-2" />
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Performing Employees</CardTitle>
            <CardDescription>Based on revenue generated</CardDescription>
          </CardHeader>
          <CardContent>
            {financialData.topEmployees.length === 0 ? (
              <p className="text-center py-4 text-muted-foreground">
                No employee data available
              </p>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Services</TableHead>
                    <TableHead>Revenue</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {financialData.topEmployees.map((employee, i) => (
                    <TableRow key={i}>
                      <TableCell className="font-medium">
                        {employee.name}
                      </TableCell>
                      <TableCell>{employee.services}</TableCell>
                      <TableCell>${employee.revenue.toFixed(2)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            {selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)}{" "}
            Revenue Trend
          </CardTitle>
          <CardDescription>
            Revenue over time (excluding cancelled bookings)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-end justify-between space-x-2">
            {financialData.periodRevenue.length === 0 ? (
              <p className="text-center py-8 text-muted-foreground w-full">
                No revenue data for selected period
              </p>
            ) : (
              financialData.periodRevenue.map((item, i) => {
                const maxRevenue = Math.max(
                  ...financialData.periodRevenue.map((d) => d.revenue),
                  1,
                );
                const height = (item.revenue / maxRevenue) * 250;
                return (
                  <div
                    key={i}
                    className="flex flex-col items-center flex-1 group"
                  >
                    <div className="relative">
                      <div className="absolute -top-8 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        ${item.revenue.toLocaleString()}
                      </div>
                      <div
                        className="bg-primary rounded-t-md transition-all hover:bg-primary/80 min-w-[20px]"
                        style={{ height: `${Math.max(height, 4)}px` }}
                      />
                    </div>
                    <span className="text-xs mt-2 text-center">
                      {item.period}
                    </span>
                  </div>
                );
              })
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Staff Schedule component
const StaffSchedule = () => {
  const [date, setDate] = useState<Date | undefined>(new Date());

  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-bold tracking-tight">My Schedule</h2>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Calendar</CardTitle>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={date}
              onSelect={setDate}
              className="rounded-md border"
            />
          </CardContent>
        </Card>

        <Card className="col-span-2">
          <CardHeader>
            <CardTitle>Appointments for {date?.toLocaleDateString()}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[10, 11, 14].map((hour, i) => (
                <div
                  key={i}
                  className="flex items-start space-x-4 p-3 rounded-lg border"
                >
                  <div className="bg-primary/10 p-2 rounded-md">
                    <Clock className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">
                        {hour}:00 {hour < 12 ? "AM" : "PM"}
                      </h4>
                      <Badge>{["Confirmed", "Confirmed", "Pending"][i]}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {["Hair Styling", "Manicure", "Hair Coloring"][i]} -
                      {["Jane Smith", "Robert Johnson", "Emily Davis"][i]}
                    </p>
                    <div className="mt-2 flex items-center text-xs text-muted-foreground">
                      <span>Duration: {[60, 45, 120][i]} min</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      Complete
                    </Button>
                    <Button variant="outline" size="sm">
                      Reschedule
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

// Import the new attendance management component
import { AttendanceManagement } from "./Attendance";
// Import the new employee management component
import { EmployeeManagement } from "./Employee";

// Record Attendance component - now uses the dedicated AttendanceManagement component
const RecordAttendance = ({ userRole }: { userRole?: "admin" | "staff" }) => {
  return <AttendanceManagement userRole={userRole} />;
};

// Staff Performance component
const StaffPerformance = () => {
  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-bold tracking-tight">My Performance</h2>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Services Completed
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">32</div>
            <p className="text-xs text-muted-foreground">+4 from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Revenue Generated
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$3,240.50</div>
            <p className="text-xs text-muted-foreground">
              +12.5% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Client Satisfaction
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.8/5.0</div>
            <p className="text-xs text-muted-foreground">Based on 28 reviews</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Attendance Rate
            </CardTitle>
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">95%</div>
            <p className="text-xs text-muted-foreground">
              1 absence this month
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Service Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { service: "Hair Styling", count: 12, revenue: 1080.0 },
              { service: "Hair Coloring", count: 8, revenue: 960.0 },
              { service: "Haircuts", count: 7, revenue: 245.0 },
              { service: "Blowouts", count: 5, revenue: 375.0 },
            ].map((item, i) => (
              <div key={i} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span>{item.service}</span>
                  <span className="text-sm">${item.revenue.toFixed(2)}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Progress value={(item.count / 12) * 100} className="h-2" />
                  <span className="text-xs">{item.count} services</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Staff Bookings component
const StaffBookings = () => {
  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-bold tracking-tight">Manage Bookings</h2>

      <Tabs defaultValue="upcoming">
        <TabsList>
          <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
        </TabsList>
        <TabsContent value="upcoming" className="space-y-4">
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                {[1, 2, 3, 4].map((_, i) => (
                  <div
                    key={i}
                    className="flex items-start space-x-4 p-3 rounded-lg border"
                  >
                    <div className="bg-primary/10 p-2 rounded-md">
                      <CalendarIcon className="h-5 w-5 text-primary" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">
                          {
                            [
                              "Hair Styling",
                              "Manicure",
                              "Facial",
                              "Hair Coloring",
                            ][i]
                          }
                        </h4>
                        <Badge>
                          {["Today", "Tomorrow", "Jun 8", "Jun 10"][i]}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Client:{" "}
                        {
                          [
                            "Jane Smith",
                            "Robert Johnson",
                            "Emily Davis",
                            "Michael Brown",
                          ][i]
                        }
                      </p>
                      <div className="mt-2 flex items-center text-xs text-muted-foreground">
                        <span className="mr-2">
                          Time:{" "}
                          {["10:00 AM", "2:30 PM", "11:15 AM", "3:45 PM"][i]}
                        </span>
                        <span>Duration: {[60, 45, 60, 120][i]} min</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        Complete
                      </Button>
                      <Button variant="outline" size="sm">
                        Reschedule
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="completed" className="space-y-4">
          <Card>
            <CardContent className="pt-6">
              <p>Completed bookings content</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="cancelled" className="space-y-4">
          <Card>
            <CardContent className="pt-6">
              <p>Cancelled bookings content</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Day Off Dialog */}
      <Dialog open={showDayOffDialog} onOpenChange={setShowDayOffDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add Employee Day Off</DialogTitle>
            <DialogDescription>
              Give specific employees a day off so they won't be marked absent
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Select Employees</Label>
              <div className="max-h-48 overflow-y-auto space-y-2 border rounded-md p-3">
                {employees
                  .filter((emp) => emp.status === "Active")
                  .map((employee) => (
                    <div
                      key={employee.id}
                      className="flex items-center space-x-2"
                    >
                      <input
                        type="checkbox"
                        id={`dayoff-${employee.id}`}
                        checked={newDayOff.employee_ids.includes(employee.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setNewDayOff({
                              ...newDayOff,
                              employee_ids: [
                                ...newDayOff.employee_ids,
                                employee.id,
                              ],
                            });
                          } else {
                            setNewDayOff({
                              ...newDayOff,
                              employee_ids: newDayOff.employee_ids.filter(
                                (id) => id !== employee.id,
                              ),
                            });
                          }
                        }}
                        className="rounded"
                      />
                      <Label
                        htmlFor={`dayoff-${employee.id}`}
                        className="flex-1"
                      >
                        {employee.name} - {employee.position}
                      </Label>
                    </div>
                  ))}
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="dayoff-date">Date</Label>
              <Input
                id="dayoff-date"
                type="date"
                value={newDayOff.date}
                onChange={(e) =>
                  setNewDayOff({ ...newDayOff, date: e.target.value })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="dayoff-reason">Reason (Optional)</Label>
              <Input
                id="dayoff-reason"
                placeholder="e.g., Personal leave, Sick day"
                value={newDayOff.reason}
                onChange={(e) =>
                  setNewDayOff({ ...newDayOff, reason: e.target.value })
                }
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowDayOffDialog(false);
                setNewDayOff({ employee_ids: [], date: "", reason: "" });
              }}
            >
              Cancel
            </Button>
            <Button onClick={addDayOff}>Add Day Off</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Settings Management component
const SettingsManagement = () => {
  const [workingDays, setWorkingDays] = useState({
    monday: true,
    tuesday: true,
    wednesday: true,
    thursday: true,
    friday: true,
    saturday: false,
    sunday: false,
  });

  const [workingHours, setWorkingHours] = useState({
    startTime: "09:00",
    endTime: "17:00",
  });

  const [holidays, setHolidays] = useState([
    { id: 1, name: "New Year's Day", date: "2024-01-01" },
    { id: 2, name: "Independence Day", date: "2024-07-04" },
    { id: 3, name: "Christmas Day", date: "2024-12-25" },
  ]);

  const [attendanceRules, setAttendanceRules] = useState({
    lateArrivalPenalty: 50, // Amount deducted for late days
    absencePenalty: 100, // Amount deducted for absent days
    allowanceTime: 15, // Minutes after start time before considered late
    overtimeReward: 25, // Amount per hour for overtime
    lateDaysThreshold: 5, // Number of late days before penalty
    absentDaysThreshold: 2, // Number of absent days before penalty
  });

  const [layoutSettings, setLayoutSettings] = useState({
    darkMode: false,
    rtlMode: false,
  });

  const [newHoliday, setNewHoliday] = useState({ name: "", date: "" });
  const [dayOffs, setDayOffs] = useState<any[]>([]);
  const [newDayOff, setNewDayOff] = useState({
    employee_ids: [] as number[],
    date: "",
    reason: "",
  });
  const [showDayOffDialog, setShowDayOffDialog] = useState(false);
  const [employees, setEmployees] = useState<any[]>([]);

  const dataManager = DatabaseManager.getInstance();

  useEffect(() => {
    const settings = dataManager.getSettings();
    setWorkingDays(settings.workingDays);
    setWorkingHours(settings.workingHours);
    setHolidays(settings.holidays);
    setAttendanceRules(settings.attendanceRules);
    setLayoutSettings(
      settings.layoutSettings || { darkMode: false, rtlMode: false },
    );
    setEmployees(dataManager.getEmployees());
    loadDayOffs();
  }, []);

  const loadDayOffs = () => {
    const savedDayOffs = localStorage.getItem("salon_day_offs");
    if (savedDayOffs) {
      setDayOffs(JSON.parse(savedDayOffs));
    }
  };

  const handleWorkingDayChange = (day: string, checked: boolean) => {
    setWorkingDays((prev) => ({ ...prev, [day]: checked }));
  };

  const handleWorkingHoursChange = (field: string, value: string) => {
    setWorkingHours((prev) => ({ ...prev, [field]: value }));
  };

  const handleAttendanceRuleChange = (field: string, value: number) => {
    setAttendanceRules((prev) => ({ ...prev, [field]: value }));
  };

  const addHoliday = () => {
    if (newHoliday.name && newHoliday.date) {
      setHolidays((prev) => [
        ...prev,
        { id: Date.now(), name: newHoliday.name, date: newHoliday.date },
      ]);
      setNewHoliday({ name: "", date: "" });
    }
  };

  const removeHoliday = (id: number) => {
    setHolidays((prev) => prev.filter((holiday) => holiday.id !== id));
  };

  const addDayOff = () => {
    if (newDayOff.employee_ids.length === 0 || !newDayOff.date) {
      alert("Please select employees and date for day off.");
      return;
    }

    const dayOff = {
      id: Date.now(),
      employee_ids: newDayOff.employee_ids,
      employee_names: newDayOff.employee_ids.map((id) => {
        const emp = employees.find((e) => e.id === id);
        return emp ? emp.name : "Unknown";
      }),
      date: newDayOff.date,
      reason: newDayOff.reason,
      created_at: new Date().toISOString(),
    };

    const updatedDayOffs = [...dayOffs, dayOff];
    setDayOffs(updatedDayOffs);
    localStorage.setItem("salon_day_offs", JSON.stringify(updatedDayOffs));

    setNewDayOff({ employee_ids: [], date: "", reason: "" });
    setShowDayOffDialog(false);
  };

  const removeDayOff = (id: number) => {
    const updatedDayOffs = dayOffs.filter((dayOff) => dayOff.id !== id);
    setDayOffs(updatedDayOffs);
    localStorage.setItem("salon_day_offs", JSON.stringify(updatedDayOffs));
  };

  const saveSettings = () => {
    const settings = {
      workingDays,
      workingHours,
      holidays,
      attendanceRules,
      layoutSettings,
    };
    dataManager.saveSettings(settings);

    // Apply dark mode to document
    if (layoutSettings.darkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }

    // Apply RTL mode to document
    if (layoutSettings.rtlMode) {
      document.documentElement.setAttribute("dir", "rtl");
      document.documentElement.classList.add("rtl");
    } else {
      document.documentElement.setAttribute("dir", "ltr");
      document.documentElement.classList.remove("rtl");
    }

    alert("Settings saved successfully!");
  };

  return (
    <div className="space-y-6 p-6 bg-background">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Settings</h2>
        <Button onClick={saveSettings}>
          <Save className="mr-2 h-4 w-4" />
          Save All Settings
        </Button>
      </div>

      <Tabs defaultValue="working-days">
        <TabsList>
          <TabsTrigger value="working-days">Working Days & Hours</TabsTrigger>
          <TabsTrigger value="holidays">Holidays & Day Offs</TabsTrigger>
          <TabsTrigger value="attendance-rules">Attendance Rules</TabsTrigger>
          <TabsTrigger value="layout">Layout</TabsTrigger>
        </TabsList>

        <TabsContent value="working-days" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Working Days</CardTitle>
              <CardDescription>
                Select which days of the week are working days
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(workingDays).map(([day, isWorking]) => (
                <div key={day} className="flex items-center justify-between">
                  <Label className="text-sm font-medium capitalize">
                    {day}
                  </Label>
                  <Switch
                    checked={isWorking}
                    onCheckedChange={(checked) =>
                      handleWorkingDayChange(day, checked)
                    }
                  />
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Working Hours</CardTitle>
              <CardDescription>
                Set the standard working hours for the salon
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start-time">Work Start Time</Label>
                  <Input
                    id="start-time"
                    type="time"
                    value={workingHours.startTime}
                    onChange={(e) =>
                      handleWorkingHoursChange("startTime", e.target.value)
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="end-time">Work End Time</Label>
                  <Input
                    id="end-time"
                    type="time"
                    value={workingHours.endTime}
                    onChange={(e) =>
                      handleWorkingHoursChange("endTime", e.target.value)
                    }
                  />
                </div>
              </div>
              <div className="p-4 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">
                  Current working hours: {workingHours.startTime} -{" "}
                  {workingHours.endTime}(
                  {(
                    (new Date(
                      `2000-01-01T${workingHours.endTime}:00`,
                    ).getTime() -
                      new Date(
                        `2000-01-01T${workingHours.startTime}:00`,
                      ).getTime()) /
                    (1000 * 60 * 60)
                  ).toFixed(1)}{" "}
                  hours per day)
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="holidays" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Holidays</CardTitle>
              <CardDescription>
                Manage salon holidays when employees are not expected to work
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="holiday-name">Holiday Name</Label>
                  <Input
                    id="holiday-name"
                    placeholder="e.g., Christmas Day"
                    value={newHoliday.name}
                    onChange={(e) =>
                      setNewHoliday((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="holiday-date">Date</Label>
                  <Input
                    id="holiday-date"
                    type="date"
                    value={newHoliday.date}
                    onChange={(e) =>
                      setNewHoliday((prev) => ({
                        ...prev,
                        date: e.target.value,
                      }))
                    }
                  />
                </div>
              </div>
              <Button onClick={addHoliday} className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                Add Holiday
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Current Holidays</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {holidays.map((holiday) => (
                  <div
                    key={holiday.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div>
                      <div className="font-medium">{holiday.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {new Date(holiday.date).toLocaleDateString()}
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeHoliday(holiday.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Employee Day Offs</CardTitle>
              <CardDescription>
                Give specific employees days off so they won't be marked absent
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={() => {
                  setNewDayOff({ employee_ids: [], date: "", reason: "" });
                  setShowDayOffDialog(true);
                }}
                className="w-full"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Day Off
              </Button>

              <div className="space-y-2">
                {dayOffs.map((dayOff) => (
                  <div
                    key={dayOff.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div>
                      <div className="font-medium">
                        {dayOff.employee_names.join(", ")}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {new Date(dayOff.date).toLocaleDateString()}
                        {dayOff.reason && ` - ${dayOff.reason}`}
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeDayOff(dayOff.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                {dayOffs.length === 0 && (
                  <p className="text-center py-4 text-muted-foreground">
                    No day offs scheduled
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="attendance-rules" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Late Arrival Penalty</CardTitle>
              <CardDescription>
                Amount deducted from salary for excessive late arrivals
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="late-days-threshold">
                    Number of late days for penalty
                  </Label>
                  <Input
                    id="late-days-threshold"
                    type="number"
                    min="1"
                    max="31"
                    value={attendanceRules.lateDaysThreshold}
                    onChange={(e) =>
                      handleAttendanceRuleChange(
                        "lateDaysThreshold",
                        parseInt(e.target.value) || 5,
                      )
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="late-penalty">Penalty amount ($)</Label>
                  <Input
                    id="late-penalty"
                    type="number"
                    min="0"
                    step="0.01"
                    value={attendanceRules.lateArrivalPenalty}
                    onChange={(e) =>
                      handleAttendanceRuleChange(
                        "lateArrivalPenalty",
                        parseFloat(e.target.value) || 0,
                      )
                    }
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="allowance-time">
                  Allowance time after start time (minutes)
                </Label>
                <Input
                  id="allowance-time"
                  type="number"
                  min="0"
                  max="60"
                  value={attendanceRules.allowanceTime}
                  onChange={(e) =>
                    handleAttendanceRuleChange(
                      "allowanceTime",
                      parseInt(e.target.value) || 0,
                    )
                  }
                />
              </div>
              <div className="p-4 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">
                  Employees arriving within {attendanceRules.allowanceTime}{" "}
                  minutes after
                  {workingHours.startTime} will not be considered late.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Absence Penalty</CardTitle>
              <CardDescription>
                Amount deducted from salary for excessive absences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="absent-days-threshold">
                    Number of absent days for penalty
                  </Label>
                  <Input
                    id="absent-days-threshold"
                    type="number"
                    min="1"
                    max="31"
                    value={attendanceRules.absentDaysThreshold}
                    onChange={(e) =>
                      handleAttendanceRuleChange(
                        "absentDaysThreshold",
                        parseInt(e.target.value) || 2,
                      )
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="absence-penalty">Penalty amount ($)</Label>
                  <Input
                    id="absence-penalty"
                    type="number"
                    min="0"
                    step="0.01"
                    value={attendanceRules.absencePenalty}
                    onChange={(e) =>
                      handleAttendanceRuleChange(
                        "absencePenalty",
                        parseFloat(e.target.value) || 0,
                      )
                    }
                  />
                </div>
              </div>
              <div className="p-4 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">
                  Absences during holidays and non-working days will not count
                  towards penalties.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Overtime Reward</CardTitle>
              <CardDescription>
                Additional compensation for working beyond standard hours
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="overtime-reward">
                  Reward per hour of overtime ($)
                </Label>
                <Input
                  id="overtime-reward"
                  type="number"
                  min="0"
                  step="0.01"
                  value={attendanceRules.overtimeReward}
                  onChange={(e) =>
                    handleAttendanceRuleChange(
                      "overtimeReward",
                      parseFloat(e.target.value) || 0,
                    )
                  }
                />
              </div>
              <div className="p-4 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">
                  Employees working after {workingHours.endTime} will receive $
                  {attendanceRules.overtimeReward} per hour as overtime
                  compensation.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Rules Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span>
                    Late arrival penalty ({attendanceRules.lateDaysThreshold}{" "}
                    days/month):
                  </span>
                  <span className="font-medium">
                    ${attendanceRules.lateArrivalPenalty}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>
                    Absence penalty ({attendanceRules.absentDaysThreshold}{" "}
                    days/month):
                  </span>
                  <span className="font-medium">
                    ${attendanceRules.absencePenalty}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Allowance time:</span>
                  <span className="font-medium">
                    {attendanceRules.allowanceTime} minutes
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Overtime reward:</span>
                  <span className="font-medium">
                    ${attendanceRules.overtimeReward}/hour
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between font-medium">
                  <span>Working hours:</span>
                  <span>
                    {workingHours.startTime} - {workingHours.endTime}
                  </span>
                </div>
                <div className="flex justify-between font-medium">
                  <span>Working days:</span>
                  <span>
                    {Object.entries(workingDays)
                      .filter(([_, isWorking]) => isWorking)
                      .map(
                        ([day]) =>
                          day.charAt(0).toUpperCase() + day.slice(1, 3),
                      )
                      .join(", ")}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="layout" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Appearance Settings</CardTitle>
              <CardDescription>
                Customize the look and feel of the application
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium">Dark Mode</Label>
                  <p className="text-sm text-muted-foreground">
                    Switch between light and dark theme
                  </p>
                </div>
                <Switch
                  checked={layoutSettings.darkMode}
                  onCheckedChange={(checked) =>
                    setLayoutSettings((prev) => ({
                      ...prev,
                      darkMode: checked,
                    }))
                  }
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium">RTL Mode</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable right-to-left layout for Arabic and other RTL
                    languages
                  </p>
                </div>
                <Switch
                  checked={layoutSettings.rtlMode}
                  onCheckedChange={(checked) =>
                    setLayoutSettings((prev) => ({ ...prev, rtlMode: checked }))
                  }
                />
              </div>
              <Separator />
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="text-sm font-medium mb-2">Preview</h4>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex justify-between">
                    <span>Theme:</span>
                    <span className="font-medium">
                      {layoutSettings.darkMode ? "Dark" : "Light"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Direction:</span>
                    <span className="font-medium">
                      {layoutSettings.rtlMode
                        ? "Right-to-Left"
                        : "Left-to-Right"}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Language & Localization</CardTitle>
              <CardDescription>
                Language and regional settings (Coming Soon)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between opacity-50">
                <div className="space-y-0.5">
                  <Label className="text-base font-medium">Language</Label>
                  <p className="text-sm text-muted-foreground">
                    Choose your preferred language
                  </p>
                </div>
                <Select disabled>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="English" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="ar">العربية</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Color Theme</CardTitle>
              <CardDescription>
                Customize the primary color scheme (Coming Soon)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="opacity-50">
                <Label className="text-base font-medium mb-3 block">
                  Primary Color
                </Label>
                <div className="grid grid-cols-6 gap-3">
                  {[
                    { name: "Blue", color: "bg-blue-500" },
                    { name: "Green", color: "bg-green-500" },
                    { name: "Purple", color: "bg-purple-500" },
                    { name: "Red", color: "bg-red-500" },
                    { name: "Orange", color: "bg-orange-500" },
                    { name: "Pink", color: "bg-pink-500" },
                  ].map((colorOption) => (
                    <div
                      key={colorOption.name}
                      className="flex flex-col items-center space-y-2 cursor-not-allowed"
                    >
                      <div
                        className={`w-8 h-8 rounded-full ${colorOption.color} border-2 border-transparent`}
                      />
                      <span className="text-xs text-muted-foreground">
                        {colorOption.name}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Customer Management component
const CustomerManagement = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddCustomerDialog, setShowAddCustomerDialog] = useState(false);
  const [showCustomerProfile, setShowCustomerProfile] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null,
  );
  const [newCustomer, setNewCustomer] = useState({
    name: "",
    phone: "",
    email: "",
    address: "",
    notes: "",
    instagram: "",
    facebook: "",
    twitter: "",
  });

  const dataManager = DatabaseManager.getInstance();

  const getStatusColor = (status: Booking["status"]) => {
    switch (status) {
      case "Confirmed":
        return "default";
      case "In Progress":
        return "secondary";
      case "Cancelled":
        return "destructive";
      case "Completed":
        return "outline";
      default:
        return "default";
    }
  };

  useEffect(() => {
    loadCustomerData();
  }, []);

  const loadCustomerData = () => {
    // Load customers from database
    const dbCustomers = dataManager.getCustomers().map((customer) => ({
      ...customer,
      favorite_services: JSON.parse(customer.favorite_services || "[]"),
    }));
    setCustomers(dbCustomers);

    // Load bookings from database
    const dbBookings = dataManager.getBookings().map((booking) => ({
      ...booking,
      service_ids: JSON.parse(booking.service_ids || "[]"),
      package_ids: JSON.parse(booking.package_ids || "[]"),
      dress_ids: JSON.parse(booking.dress_ids || "[]"),
      employee_ids: JSON.parse(booking.employee_ids || "[]"),
    }));
    setBookings(dbBookings);
  };

  const handleAddCustomer = () => {
    if (!newCustomer.name) {
      alert("Please enter customer name.");
      return;
    }

    const customerData = {
      ...newCustomer,
      total_bookings: 0,
      total_spent: 0,
      favorite_services: JSON.stringify([]),
    };

    const customer = dataManager.addCustomer(customerData);
    const customerWithParsedServices = {
      ...customer,
      favorite_services: JSON.parse(customer.favorite_services || "[]"),
    };

    const updatedCustomers = [...customers, customerWithParsedServices];
    setCustomers(updatedCustomers);

    setShowAddCustomerDialog(false);
    setNewCustomer({
      name: "",
      phone: "",
      email: "",
      address: "",
      notes: "",
      instagram: "",
      facebook: "",
      twitter: "",
    });
  };

  const handleViewProfile = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowCustomerProfile(true);
  };

  const handleUpdateCustomer = () => {
    if (!selectedCustomer) return;

    const customerData = {
      ...selectedCustomer,
      favorite_services: JSON.stringify(
        selectedCustomer.favorite_services || [],
      ),
    };

    dataManager.updateCustomer(selectedCustomer.id, customerData);
    const updatedCustomers = customers.map((c) =>
      c.id === selectedCustomer.id
        ? { ...selectedCustomer, updated_at: new Date().toISOString() }
        : c,
    );
    setCustomers(updatedCustomers);
    setShowCustomerProfile(false);
  };

  const getCustomerBookings = (customerId: number) => {
    return bookings.filter((booking) => booking.customer_id === customerId);
  };

  const getUpcomingBookings = (customerId: number) => {
    const today = new Date().toISOString().split("T")[0];
    return bookings.filter(
      (booking) =>
        booking.customer_id === customerId &&
        booking.date >= today &&
        booking.status !== "Cancelled",
    );
  };

  const getPastBookings = (customerId: number) => {
    const today = new Date().toISOString().split("T")[0];
    return bookings.filter(
      (booking) =>
        booking.customer_id === customerId &&
        (booking.date < today || booking.status === "Completed"),
    );
  };

  const filteredCustomers = customers.filter(
    (customer) =>
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (customer.email &&
        customer.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (customer.phone && customer.phone.includes(searchTerm)),
  );

  // Calculate analytics
  const totalCustomers = customers.length;
  const totalRevenue = customers.reduce((sum, c) => sum + c.total_spent, 0);
  const avgSpentPerCustomer =
    totalCustomers > 0 ? totalRevenue / totalCustomers : 0;
  const topCustomer = customers.reduce(
    (top, current) => (current.total_spent > top.total_spent ? current : top),
    customers[0] || { total_spent: 0 },
  );

  return (
    <div className="space-y-6 p-6 bg-background">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">
          Customer Management
        </h2>
        <Button onClick={() => setShowAddCustomerDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Customer
        </Button>
      </div>

      {/* Analytics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Customers
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCustomers}</div>
            <p className="text-xs text-muted-foreground">
              +{Math.floor(totalCustomers * 0.1)} this month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${totalRevenue.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">From all customers</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Spent</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${avgSpentPerCustomer.toFixed(0)}
            </div>
            <p className="text-xs text-muted-foreground">Per customer</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Customer</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${topCustomer.total_spent}</div>
            <p className="text-xs text-muted-foreground">
              {topCustomer.name || "No customers"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Search customers..."
            className="max-w-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Button variant="outline" size="icon">
            <Search className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Customer List */}
      <Card>
        <CardHeader>
          <CardTitle>All Customers</CardTitle>
          <CardDescription>
            Manage customer information and view their booking history
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Customer</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Total Bookings</TableHead>
                <TableHead>Total Spent</TableHead>
                <TableHead>Last Visit</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCustomers.map((customer) => (
                <TableRow key={customer.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>
                          {customer.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{customer.name}</div>
                        <div className="text-sm text-muted-foreground">
                          Customer since{" "}
                          {new Date(customer.created_at).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {customer.phone && (
                        <div className="flex items-center text-sm">
                          <Phone className="h-3 w-3 mr-1" />
                          {customer.phone}
                        </div>
                      )}
                      {customer.email && (
                        <div className="flex items-center text-sm">
                          <Mail className="h-3 w-3 mr-1" />
                          {customer.email}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{customer.total_bookings}</Badge>
                  </TableCell>
                  <TableCell className="font-medium">
                    ${customer.total_spent.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {customer.last_visit
                      ? new Date(customer.last_visit).toLocaleDateString()
                      : "Never"}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewProfile(customer)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Profile
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add Customer Dialog */}
      <Dialog
        open={showAddCustomerDialog}
        onOpenChange={setShowAddCustomerDialog}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Customer</DialogTitle>
            <DialogDescription>
              Enter customer information to add them to the system
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="customer-name">Full Name *</Label>
                <Input
                  id="customer-name"
                  value={newCustomer.name}
                  onChange={(e) =>
                    setNewCustomer({ ...newCustomer, name: e.target.value })
                  }
                  placeholder="Enter full name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="customer-phone">Phone</Label>
                <Input
                  id="customer-phone"
                  value={newCustomer.phone}
                  onChange={(e) =>
                    setNewCustomer({ ...newCustomer, phone: e.target.value })
                  }
                  placeholder="Enter phone number"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="customer-email">Email</Label>
              <Input
                id="customer-email"
                type="email"
                value={newCustomer.email}
                onChange={(e) =>
                  setNewCustomer({ ...newCustomer, email: e.target.value })
                }
                placeholder="Enter email address"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="customer-address">Address</Label>
              <Input
                id="customer-address"
                value={newCustomer.address}
                onChange={(e) =>
                  setNewCustomer({ ...newCustomer, address: e.target.value })
                }
                placeholder="Enter address"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="customer-notes">Notes</Label>
              <Textarea
                id="customer-notes"
                value={newCustomer.notes}
                onChange={(e) =>
                  setNewCustomer({ ...newCustomer, notes: e.target.value })
                }
                placeholder="Enter any notes about the customer"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="customer-instagram">Instagram</Label>
                <Input
                  id="customer-instagram"
                  value={newCustomer.instagram}
                  onChange={(e) =>
                    setNewCustomer({
                      ...newCustomer,
                      instagram: e.target.value,
                    })
                  }
                  placeholder="@username"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="customer-facebook">Facebook</Label>
                <Input
                  id="customer-facebook"
                  value={newCustomer.facebook}
                  onChange={(e) =>
                    setNewCustomer({ ...newCustomer, facebook: e.target.value })
                  }
                  placeholder="facebook.com/username"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="customer-twitter">Twitter</Label>
                <Input
                  id="customer-twitter"
                  value={newCustomer.twitter}
                  onChange={(e) =>
                    setNewCustomer({ ...newCustomer, twitter: e.target.value })
                  }
                  placeholder="@username"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAddCustomerDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleAddCustomer}>Add Customer</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Customer Profile Dialog */}
      <Dialog open={showCustomerProfile} onOpenChange={setShowCustomerProfile}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Customer Profile</DialogTitle>
            <DialogDescription>
              View and edit customer information and booking history
            </DialogDescription>
          </DialogHeader>
          {selectedCustomer && (
            <div className="grid gap-6 py-4">
              {/* Customer Info */}
              <div className="grid grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <User className="h-5 w-5" />
                      <span>Basic Information</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="profile-name">Full Name</Label>
                      <Input
                        id="profile-name"
                        value={selectedCustomer.name}
                        onChange={(e) =>
                          setSelectedCustomer({
                            ...selectedCustomer,
                            name: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="profile-phone">Phone</Label>
                      <Input
                        id="profile-phone"
                        value={selectedCustomer.phone || ""}
                        onChange={(e) =>
                          setSelectedCustomer({
                            ...selectedCustomer,
                            phone: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="profile-email">Email</Label>
                      <Input
                        id="profile-email"
                        type="email"
                        value={selectedCustomer.email || ""}
                        onChange={(e) =>
                          setSelectedCustomer({
                            ...selectedCustomer,
                            email: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="profile-address">Address</Label>
                      <Input
                        id="profile-address"
                        value={selectedCustomer.address || ""}
                        onChange={(e) =>
                          setSelectedCustomer({
                            ...selectedCustomer,
                            address: e.target.value,
                          })
                        }
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Customer Stats</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Total Bookings:
                        </span>
                        <span className="font-medium">
                          {selectedCustomer.total_bookings}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Total Spent:
                        </span>
                        <span className="font-medium">
                          ${selectedCustomer.total_spent.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Last Visit:
                        </span>
                        <span className="font-medium">
                          {selectedCustomer.last_visit
                            ? new Date(
                                selectedCustomer.last_visit,
                              ).toLocaleDateString()
                            : "Never"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Customer Since:
                        </span>
                        <span className="font-medium">
                          {new Date(
                            selectedCustomer.created_at,
                          ).toLocaleDateString()}
                        </span>
                      </div>
                      {selectedCustomer.favorite_services &&
                        selectedCustomer.favorite_services.length > 0 && (
                          <div>
                            <span className="text-sm text-muted-foreground">
                              Favorite Services:
                            </span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {selectedCustomer.favorite_services.map(
                                (service, index) => (
                                  <Badge
                                    key={index}
                                    variant="outline"
                                    className="text-xs"
                                  >
                                    {service}
                                  </Badge>
                                ),
                              )}
                            </div>
                          </div>
                        )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Notes and Social Media */}
              <div className="grid grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Notes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Textarea
                      value={selectedCustomer.notes || ""}
                      onChange={(e) =>
                        setSelectedCustomer({
                          ...selectedCustomer,
                          notes: e.target.value,
                        })
                      }
                      placeholder="Add notes about this customer..."
                      rows={4}
                    />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Social Media</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="space-y-2">
                      <Label htmlFor="profile-instagram">Instagram</Label>
                      <Input
                        id="profile-instagram"
                        value={selectedCustomer.instagram || ""}
                        onChange={(e) =>
                          setSelectedCustomer({
                            ...selectedCustomer,
                            instagram: e.target.value,
                          })
                        }
                        placeholder="@username"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="profile-facebook">Facebook</Label>
                      <Input
                        id="profile-facebook"
                        value={selectedCustomer.facebook || ""}
                        onChange={(e) =>
                          setSelectedCustomer({
                            ...selectedCustomer,
                            facebook: e.target.value,
                          })
                        }
                        placeholder="facebook.com/username"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="profile-twitter">Twitter</Label>
                      <Input
                        id="profile-twitter"
                        value={selectedCustomer.twitter || ""}
                        onChange={(e) =>
                          setSelectedCustomer({
                            ...selectedCustomer,
                            twitter: e.target.value,
                          })
                        }
                        placeholder="@username"
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Booking History */}
              <Tabs defaultValue="upcoming">
                <TabsList>
                  <TabsTrigger value="upcoming">
                    Upcoming Bookings (
                    {getUpcomingBookings(selectedCustomer.id).length})
                  </TabsTrigger>
                  <TabsTrigger value="history">
                    History ({getPastBookings(selectedCustomer.id).length})
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="upcoming" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <CalendarDays className="h-5 w-5" />
                        <span>Upcoming Bookings</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {getUpcomingBookings(selectedCustomer.id).length === 0 ? (
                        <p className="text-center py-8 text-muted-foreground">
                          No upcoming bookings
                        </p>
                      ) : (
                        <div className="space-y-4">
                          {getUpcomingBookings(selectedCustomer.id).map(
                            (booking) => (
                              <div
                                key={booking.id}
                                className="flex items-start space-x-4 p-3 rounded-lg border"
                              >
                                <div className="bg-primary/10 p-2 rounded-md">
                                  <CalendarDays className="h-5 w-5 text-primary" />
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center justify-between">
                                    <h4 className="font-medium">
                                      {booking.title}
                                    </h4>
                                    <Badge
                                      variant={getStatusColor(booking.status)}
                                    >
                                      {booking.status}
                                    </Badge>
                                  </div>
                                  <p className="text-sm text-muted-foreground">
                                    {new Date(
                                      booking.date,
                                    ).toLocaleDateString()}
                                    {booking.start_time &&
                                      ` at ${booking.start_time}`}
                                  </p>
                                  <p className="text-sm font-medium">
                                    ${booking.total_amount}
                                  </p>
                                  {booking.notes && (
                                    <p className="text-xs text-muted-foreground mt-1">
                                      {booking.notes}
                                    </p>
                                  )}
                                </div>
                              </div>
                            ),
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
                <TabsContent value="history" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Clock className="h-5 w-5" />
                        <span>Booking History</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {getPastBookings(selectedCustomer.id).length === 0 ? (
                        <p className="text-center py-8 text-muted-foreground">
                          No booking history
                        </p>
                      ) : (
                        <div className="space-y-4">
                          {getPastBookings(selectedCustomer.id)
                            .sort(
                              (a, b) =>
                                new Date(b.date).getTime() -
                                new Date(a.date).getTime(),
                            )
                            .map((booking) => (
                              <div
                                key={booking.id}
                                className="flex items-start space-x-4 p-3 rounded-lg border"
                              >
                                <div className="bg-muted p-2 rounded-md">
                                  <Clock className="h-5 w-5 text-muted-foreground" />
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center justify-between">
                                    <h4 className="font-medium">
                                      {booking.title}
                                    </h4>
                                    <Badge
                                      variant={getStatusColor(booking.status)}
                                    >
                                      {booking.status}
                                    </Badge>
                                  </div>
                                  <p className="text-sm text-muted-foreground">
                                    {new Date(
                                      booking.date,
                                    ).toLocaleDateString()}
                                    {booking.start_time &&
                                      ` at ${booking.start_time}`}
                                  </p>
                                  <p className="text-sm font-medium">
                                    ${booking.total_amount}
                                  </p>
                                  {booking.notes && (
                                    <p className="text-xs text-muted-foreground mt-1">
                                      {booking.notes}
                                    </p>
                                  )}
                                </div>
                              </div>
                            ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCustomerProfile(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleUpdateCustomer}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ContentArea;
