import React, { useState } from "react";
import Sidebar from "./Sidebar";
import ContentArea from "./ContentArea";

interface User {
  id: string;
  name: string;
  role: "admin" | "staff";
  avatar?: string;
}

interface DashboardLayoutProps {
  user: User;
  onLogout: () => void;
  activeSection?: string;
}

const DashboardLayout = ({
  user,
  onLogout,
  activeSection: propActiveSection,
}: DashboardLayoutProps) => {
  const [activeSection, setActiveSection] = useState<string>(
    propActiveSection || (user?.role === "admin" ? "dashboard" : "schedule"),
  );

  // Return loading state if user is not defined
  if (!user) {
    return (
      <div className="flex h-screen bg-background items-center justify-center">
        <div className="text-lg text-muted-foreground">Loading...</div>
      </div>
    );
  }

  // Handle navigation item selection
  const handleNavigation = (section: string) => {
    setActiveSection(section);
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <Sidebar
        userRole={user.role}
        activeSection={activeSection}
        onNavigate={handleNavigation}
        onLogout={onLogout}
      />

      {/* Main Content Area */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Header */}
        <header className="flex items-center justify-between px-6 py-4 border-b bg-card">
          <h1 className="text-2xl font-semibold text-foreground">
            {activeSection.charAt(0).toUpperCase() + activeSection.slice(1)}
          </h1>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-muted-foreground">
              <span>Welcome, </span>
              <span className="font-medium text-foreground">{user.name}</span>
            </div>
            <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground">
              {user.name.charAt(0)}
            </div>
          </div>
        </header>

        {/* Content Area */}
        <main className="flex-1 overflow-auto">
          <ContentArea activeSection={activeSection} userRole={user.role} />
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
