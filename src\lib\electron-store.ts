// Electron store utilities for database path management
export const getElectronAPI = () => {
  if (typeof window !== "undefined" && window.require) {
    try {
      const { ipc<PERSON><PERSON><PERSON> } = window.require("electron");
      const { app } =
        window.require("@electron/remote") || window.require("electron").remote;
      return { ipc<PERSON><PERSON><PERSON>, app };
    } catch (error) {
      console.warn("Electron APIs not available:", error);
      return null;
    }
  }
  return null;
};

export const isElectron = () => {
  return (
    typeof window !== "undefined" &&
    window.process &&
    window.process.type === "renderer"
  );
};

export const getUserDataPath = () => {
  const electronAPI = getElectronAPI();
  if (electronAPI && electronAPI.app) {
    return electronAPI.app.getPath("userData");
  }
  // Fallback for development
  return process.cwd();
};
